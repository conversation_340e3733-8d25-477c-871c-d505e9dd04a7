/* eslint-disable */
import { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  /** A datetime string with format `Y-m-d H:i:s`, e.g. `2018-05-23 13:43:32`. */
  DateTime: { input: any; output: any; }
  /** Can be used as an argument to upload files using https://github.com/jaydenseric/graphql-multipart-request-spec */
  Upload: { input: any; output: any; }
};

export type AppImage = {
  __typename?: 'AppImage';
  hash?: Maybe<Scalars['String']['output']>;
  height?: Maybe<Scalars['Float']['output']>;
  id: Scalars['ID']['output'];
  path: Scalars['String']['output'];
  width?: Maybe<Scalars['Float']['output']>;
};

export type AppPaginatorInfo = {
  __typename?: 'AppPaginatorInfo';
  currentPage: Scalars['Int']['output'];
  hasMorePages: Scalars['Boolean']['output'];
  lastPage: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type AppSetting = {
  __typename?: 'AppSetting';
  id: Scalars['ID']['output'];
  key: Scalars['String']['output'];
  value?: Maybe<Scalars['String']['output']>;
};

export type AuthResponse = {
  __typename?: 'AuthResponse';
  token?: Maybe<Scalars['String']['output']>;
  user?: Maybe<User>;
};

export type ConvenienceFee = {
  __typename?: 'ConvenienceFee';
  amount: Scalars['Float']['output'];
};

export type Coupon = {
  __typename?: 'Coupon';
  /** Number of times this coupon has been used */
  coupon_usage_count: Scalars['Int']['output'];
  discount_percent: Scalars['Float']['output'];
  event: Event;
  event_id: Scalars['ID']['output'];
  expiry_date: Scalars['DateTime']['output'];
  id: Scalars['ID']['output'];
  is_published: Scalars['Boolean']['output'];
  max_usage?: Maybe<Scalars['Int']['output']>;
  name: Scalars['String']['output'];
};

export type CreateCouponInput = {
  discount_percent: Scalars['Float']['input'];
  event_id: Scalars['ID']['input'];
  expiry_date: Scalars['DateTime']['input'];
  is_published?: InputMaybe<Scalars['Boolean']['input']>;
  /** max number of times this coupon can be used */
  max_usage?: InputMaybe<Scalars['Int']['input']>;
  name: Scalars['String']['input'];
};

export type CreateEventTicketTypeInput = {
  id?: InputMaybe<Scalars['ID']['input']>;
  is_convenience_fee_incl: Scalars['Boolean']['input'];
  is_gst_incl: Scalars['Boolean']['input'];
  maximum_capacity: Scalars['Int']['input'];
  price: Scalars['Float']['input'];
  /** Optional start date */
  start_date?: InputMaybe<Scalars['DateTime']['input']>;
  ticket_type: Scalars['String']['input'];
};

export type Event = {
  __typename?: 'Event';
  address: Scalars['String']['output'];
  artists?: Maybe<Array<Maybe<EventArtist>>>;
  /** used for filtering events by city */
  city?: Maybe<Scalars['String']['output']>;
  coupons?: Maybe<Array<Maybe<Coupon>>>;
  description?: Maybe<Scalars['String']['output']>;
  end_date: Scalars['DateTime']['output'];
  eventTicketTypes?: Maybe<Array<Maybe<EventTicketType>>>;
  faq?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  images?: Maybe<Array<Maybe<AppImage>>>;
  /** Cannot be booked by public. Only for invitees */
  is_private_event: Scalars['Boolean']['output'];
  is_published: Scalars['Boolean']['output'];
  location?: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
  open_mic_link?: Maybe<Scalars['String']['output']>;
  /** default is 0 */
  order: Scalars['Int']['output'];
  org_contact_number: Scalars['String']['output'];
  organizer_name: Scalars['String']['output'];
  seatingPlan?: Maybe<AppImage>;
  slug: Scalars['String']['output'];
  sportTicketTypes?: Maybe<Array<Maybe<SportTicketType>>>;
  start_booking_date?: Maybe<Scalars['DateTime']['output']>;
  start_date: Scalars['DateTime']['output'];
  /** default is general */
  type: Scalars['String']['output'];
  user: User;
};

export type EventArtist = {
  __typename?: 'EventArtist';
  avatar: Scalars['String']['output'];
  hash: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  order?: Maybe<Scalars['Int']['output']>;
  social_link?: Maybe<Scalars['String']['output']>;
};

export type EventArtistsInput = {
  artist_name: Scalars['String']['input'];
  avatar: Scalars['Upload']['input'];
  social_link?: InputMaybe<Scalars['String']['input']>;
};

export type EventAttendeesResponse = {
  __typename?: 'EventAttendeesResponse';
  data?: Maybe<Array<Maybe<EventTicketPurchase>>>;
  paginatorInfo?: Maybe<EventAttendeesResponsePaginatorInfo>;
};

export type EventAttendeesResponsePaginatorInfo = {
  __typename?: 'EventAttendeesResponsePaginatorInfo';
  currentPage: Scalars['Int']['output'];
  hasMorePages: Scalars['Boolean']['output'];
  lastPage: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type EventCheckIns = {
  __typename?: 'EventCheckIns';
  checkedInBy?: Maybe<User>;
  checked_in_by: Scalars['ID']['output'];
  created_at: Scalars['DateTime']['output'];
  eventTicketPurchase?: Maybe<EventTicketPurchase>;
  event_ticket_purchase_id: Scalars['ID']['output'];
  id: Scalars['ID']['output'];
  updated_at: Scalars['DateTime']['output'];
};

/** A paginated list of Event items. */
export type EventPaginator = {
  __typename?: 'EventPaginator';
  /** A list of Event items. */
  data: Array<Event>;
  /** Pagination information about the list of items. */
  paginatorInfo: PaginatorInfo;
};

export type EventTicketPurchase = {
  __typename?: 'EventTicketPurchase';
  amount_paid: Scalars['Float']['output'];
  /** Organizers contact number */
  contact_number: Scalars['String']['output'];
  convenience_fee?: Maybe<Scalars['Float']['output']>;
  coupon?: Maybe<Coupon>;
  created_at: Scalars['DateTime']['output'];
  event?: Maybe<Event>;
  eventTicketType?: Maybe<EventTicketType>;
  event_id: Scalars['ID']['output'];
  id: Scalars['ID']['output'];
  is_gift?: Maybe<Scalars['Boolean']['output']>;
  /** User mobile number */
  mobile_number?: Maybe<Scalars['String']['output']>;
  number_of_tickets: Scalars['Int']['output'];
  paymentOrder?: Maybe<PaymentOrder>;
  /** Ticket payment status. Will be set according to status received from 3rd party payment provider Note: this is not 3rd party payment provider status */
  payment_status: Scalars['String']['output'];
  pdf_media_id?: Maybe<Scalars['String']['output']>;
  qrcode?: Maybe<Scalars['String']['output']>;
  recipient_message?: Maybe<Scalars['String']['output']>;
  recipient_name?: Maybe<Scalars['String']['output']>;
  recipient_phone?: Maybe<Scalars['String']['output']>;
  scanned_at?: Maybe<Scalars['DateTime']['output']>;
  scanned_by?: Maybe<Scalars['ID']['output']>;
  ticket_path?: Maybe<Scalars['String']['output']>;
  tickets_scanned_count: Scalars['Int']['output'];
  tip_amount?: Maybe<Scalars['Float']['output']>;
  tip_message?: Maybe<Scalars['String']['output']>;
  username?: Maybe<Scalars['String']['output']>;
  whatsapp_sent_status?: Maybe<Scalars['String']['output']>;
};

/** A paginated list of EventTicketPurchase items. */
export type EventTicketPurchasePaginator = {
  __typename?: 'EventTicketPurchasePaginator';
  /** A list of EventTicketPurchase items. */
  data: Array<EventTicketPurchase>;
  /** Pagination information about the list of items. */
  paginatorInfo: PaginatorInfo;
};

export type EventTicketType = {
  __typename?: 'EventTicketType';
  event?: Maybe<Event>;
  id: Scalars['ID']['output'];
  is_convenience_fee_incl: Scalars['Boolean']['output'];
  is_gst_incl: Scalars['Boolean']['output'];
  maximum_capacity: Scalars['Int']['output'];
  price: Scalars['String']['output'];
  start_date?: Maybe<Scalars['DateTime']['output']>;
  /** Must be used sparringly. possible types : `sold_out` | `filling_fast` | `few_left` | `available` */
  status: Scalars['String']['output'];
  /** `sold_out` | `filling_fast` | `few_left` */
  tags?: Maybe<Scalars['String']['output']>;
  ticket_type: Scalars['String']['output'];
};

export enum EventType {
  General = 'GENERAL',
  Msl = 'MSL',
  VanapaHallSeated = 'VANAPA_HALL_SEATED'
}

/** Non-Sport guest list */
export type GeneralGuestList = {
  __typename?: 'GeneralGuestList';
  data?: Maybe<Array<EventTicketPurchase>>;
  paginatorInfo: AppPaginatorInfo;
};

export type GenericResponse = {
  __typename?: 'GenericResponse';
  message: Scalars['String']['output'];
};

export type GetEventsPaginator = {
  __typename?: 'GetEventsPaginator';
  data?: Maybe<Array<Event>>;
  paginatorInfo: AppPaginatorInfo;
};

export type GuestListResponse = GeneralGuestList | SportGuestList;

export type LoginResponse = {
  __typename?: 'LoginResponse';
  token: Scalars['String']['output'];
  user: User;
};

export type Mutation = {
  __typename?: 'Mutation';
  addInvitee?: Maybe<EventTicketPurchase>;
  /** only for superadmin */
  addOrganizer?: Maybe<SuccessResponse>;
  addSpotlight: GenericResponse;
  addStaff?: Maybe<User>;
  adminLogin?: Maybe<LoginResponse>;
  buyTicketOffline: GenericResponse;
  createCoupon?: Maybe<GenericResponse>;
  createEvent?: Maybe<Event>;
  createOrganizer?: Maybe<GenericResponse>;
  createPayment?: Maybe<OrderResponse>;
  createPaymentForSeatedTicket?: Maybe<OrderResponse>;
  createSportEventPayment: OrderResponse;
  deleteCoupon?: Maybe<Coupon>;
  deleteEvent?: Maybe<Event>;
  deleteEventArtist?: Maybe<SuccessResponse>;
  deleteImageById?: Maybe<SuccessResponse>;
  deleteOrganizer?: Maybe<GenericResponse>;
  deleteSettingByKey?: Maybe<AppSetting>;
  deleteSpotlight: GenericResponse;
  deleteStaff?: Maybe<SuccessResponse>;
  deleteTicketType?: Maybe<EventTicketType>;
  exportGuestList?: Maybe<GenericResponse>;
  /** Generate ticket and send it to mobile_number */
  generateTicket?: Maybe<SuccessResponse>;
  generateTicketForTicketPurchase?: Maybe<GenericResponse>;
  getSettings?: Maybe<Array<Maybe<AppSetting>>>;
  issueRefund?: Maybe<RefundResponse>;
  logout?: Maybe<SuccessResponse>;
  notifyUpcomingEvent?: Maybe<Event>;
  onboardOrganizer?: Maybe<OrganizerOnBoarding>;
  redeemCoupon?: Maybe<RedeemCouponResponse>;
  refundTicket?: Maybe<GenericResponse>;
  resendTicket?: Maybe<GenericResponse>;
  sendWhatsAppToInvitees?: Maybe<SuccessResponse>;
  setConvenienceFeeForEvent?: Maybe<SuccessResponse>;
  submitQRCode?: Maybe<SubmitQrCodeResponse>;
  supportLogin?: Maybe<LoginResponse>;
  updateCoupon?: Maybe<Coupon>;
  updateEvent?: Maybe<Event>;
  /** only for superadmin */
  updateOrganizer?: Maybe<User>;
  updateSpotlight: GenericResponse;
  updateStaff?: Maybe<User>;
  upsertEventArtist?: Maybe<EventArtist>;
  upsertSetting?: Maybe<AppSetting>;
  userLogin?: Maybe<User>;
  userRequestRefund?: Maybe<GenericResponse>;
};


export type MutationAddInviteeArgs = {
  event_id: Scalars['ID']['input'];
  mobile_number: Scalars['String']['input'];
  number_of_tickets: Scalars['Int']['input'];
  username: Scalars['String']['input'];
};


export type MutationAddOrganizerArgs = {
  name: Scalars['String']['input'];
  password: Scalars['String']['input'];
};


export type MutationAddSpotlightArgs = {
  image: Scalars['Upload']['input'];
  is_external?: InputMaybe<Scalars['Boolean']['input']>;
  order?: InputMaybe<Scalars['Int']['input']>;
  url?: InputMaybe<Scalars['String']['input']>;
};


export type MutationAddStaffArgs = {
  password: Scalars['String']['input'];
  username: Scalars['String']['input'];
};


export type MutationAdminLoginArgs = {
  password: Scalars['String']['input'];
  username: Scalars['String']['input'];
};


export type MutationBuyTicketOfflineArgs = {
  event_type: Scalars['String']['input'];
  mobile_number: Scalars['String']['input'];
  number_of_tickets?: InputMaybe<Scalars['Int']['input']>;
  seat_numbers?: InputMaybe<Array<Scalars['Int']['input']>>;
  ticket_type_id: Scalars['Int']['input'];
  username: Scalars['String']['input'];
};


export type MutationCreateCouponArgs = {
  data: Array<CreateCouponInput>;
};


export type MutationCreateEventArgs = {
  address: Scalars['String']['input'];
  artists?: InputMaybe<Array<InputMaybe<EventArtistsInput>>>;
  city: Scalars['String']['input'];
  convenience_fee?: InputMaybe<Scalars['Float']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  end_date: Scalars['DateTime']['input'];
  event_images: Array<InputMaybe<Scalars['Upload']['input']>>;
  event_ticket_types?: InputMaybe<Array<CreateEventTicketTypeInput>>;
  event_type?: InputMaybe<EventType>;
  faq?: InputMaybe<Scalars['String']['input']>;
  is_private_event?: InputMaybe<Scalars['Boolean']['input']>;
  is_published: Scalars['Boolean']['input'];
  location: Scalars['String']['input'];
  name: Scalars['String']['input'];
  open_mic_link?: InputMaybe<Scalars['String']['input']>;
  order?: InputMaybe<Scalars['Int']['input']>;
  org_contact_number: Scalars['String']['input'];
  organizer_name: Scalars['String']['input'];
  start_booking_date?: InputMaybe<Scalars['DateTime']['input']>;
  start_date: Scalars['DateTime']['input'];
};


export type MutationCreateOrganizerArgs = {
  password: Scalars['String']['input'];
  username: Scalars['String']['input'];
};


export type MutationCreatePaymentArgs = {
  coupon_hash?: InputMaybe<Scalars['String']['input']>;
  event_ticket_type_id: Scalars['ID']['input'];
  is_gift?: InputMaybe<Scalars['Boolean']['input']>;
  mobile_number: Scalars['String']['input'];
  num_of_tickets: Scalars['Int']['input'];
  recipient_message?: InputMaybe<Scalars['String']['input']>;
  recipient_name?: InputMaybe<Scalars['String']['input']>;
  recipient_phone?: InputMaybe<Scalars['String']['input']>;
  tip_amount?: InputMaybe<Scalars['Float']['input']>;
  tip_message?: InputMaybe<Scalars['String']['input']>;
  username: Scalars['String']['input'];
};


export type MutationCreatePaymentForSeatedTicketArgs = {
  event_id: Scalars['Int']['input'];
  mobile_number: Scalars['String']['input'];
  seat_numbers: Array<Scalars['String']['input']>;
  tip_amount?: InputMaybe<Scalars['Float']['input']>;
  tip_message?: InputMaybe<Scalars['String']['input']>;
  username: Scalars['String']['input'];
};


export type MutationCreateSportEventPaymentArgs = {
  is_gift?: InputMaybe<Scalars['Boolean']['input']>;
  mobile_number: Scalars['String']['input'];
  number_of_tickets?: InputMaybe<Scalars['Int']['input']>;
  recipient_message?: InputMaybe<Scalars['String']['input']>;
  recipient_name?: InputMaybe<Scalars['String']['input']>;
  seat_numbers: Array<Scalars['Int']['input']>;
  ticket_type_id: Scalars['Int']['input'];
  tip_amount?: InputMaybe<Scalars['Float']['input']>;
  tip_message?: InputMaybe<Scalars['String']['input']>;
  username: Scalars['String']['input'];
};


export type MutationDeleteCouponArgs = {
  coupon_id: Scalars['ID']['input'];
};


export type MutationDeleteEventArgs = {
  id: Scalars['ID']['input'];
};


export type MutationDeleteEventArtistArgs = {
  event_artist_id?: InputMaybe<Scalars['ID']['input']>;
};


export type MutationDeleteImageByIdArgs = {
  id?: InputMaybe<Scalars['ID']['input']>;
};


export type MutationDeleteOrganizerArgs = {
  id: Scalars['ID']['input'];
};


export type MutationDeleteSettingByKeyArgs = {
  key: Scalars['String']['input'];
};


export type MutationDeleteSpotlightArgs = {
  id: Scalars['Int']['input'];
};


export type MutationDeleteStaffArgs = {
  id: Scalars['ID']['input'];
};


export type MutationDeleteTicketTypeArgs = {
  id: Scalars['ID']['input'];
};


export type MutationExportGuestListArgs = {
  event_id?: InputMaybe<Scalars['Int']['input']>;
  phone_number: Scalars['String']['input'];
};


export type MutationGenerateTicketArgs = {
  event_id: Scalars['ID']['input'];
  event_ticket_type_id: Scalars['ID']['input'];
  mobile_number: Scalars['String']['input'];
  num_of_tickets: Scalars['Int']['input'];
  username: Scalars['String']['input'];
};


export type MutationGenerateTicketForTicketPurchaseArgs = {
  event_ticket_purchase_id: Scalars['ID']['input'];
};


export type MutationIssueRefundArgs = {
  amount: Scalars['Float']['input'];
  order_id: Scalars['String']['input'];
};


export type MutationNotifyUpcomingEventArgs = {
  id: Scalars['ID']['input'];
};


export type MutationOnboardOrganizerArgs = {
  address?: InputMaybe<Scalars['String']['input']>;
  email: Scalars['String']['input'];
  mobile_number: Scalars['String']['input'];
  name: Scalars['String']['input'];
};


export type MutationRedeemCouponArgs = {
  coupon: Scalars['String']['input'];
  event_id: Scalars['ID']['input'];
};


export type MutationRefundTicketArgs = {
  event_ticket_purchase_id: Scalars['ID']['input'];
};


export type MutationResendTicketArgs = {
  event_ticket_purchase_id: Scalars['ID']['input'];
};


export type MutationSendWhatsAppToInviteesArgs = {
  event_id: Scalars['ID']['input'];
  id: Array<Scalars['ID']['input']>;
};


export type MutationSetConvenienceFeeForEventArgs = {
  convenience_fee: Scalars['Float']['input'];
  event_id: Scalars['ID']['input'];
};


export type MutationSubmitQrCodeArgs = {
  no_of_person_scanned?: InputMaybe<Scalars['Int']['input']>;
  qr_code: Scalars['String']['input'];
};


export type MutationSupportLoginArgs = {
  password?: InputMaybe<Scalars['String']['input']>;
  username: Scalars['String']['input'];
};


export type MutationUpdateCouponArgs = {
  coupon_id: Scalars['ID']['input'];
  discount_percent?: InputMaybe<Scalars['Float']['input']>;
  event_id?: InputMaybe<Scalars['ID']['input']>;
  expiry_date?: InputMaybe<Scalars['DateTime']['input']>;
  is_published?: InputMaybe<Scalars['Boolean']['input']>;
  max_usage?: InputMaybe<Scalars['Int']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
};


export type MutationUpdateEventArgs = {
  address?: InputMaybe<Scalars['String']['input']>;
  city?: InputMaybe<Scalars['String']['input']>;
  convenience_fee?: InputMaybe<Scalars['Float']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  end_date?: InputMaybe<Scalars['DateTime']['input']>;
  event_images?: InputMaybe<Array<InputMaybe<Scalars['Upload']['input']>>>;
  event_type?: InputMaybe<EventType>;
  faq?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['ID']['input'];
  is_private_event?: InputMaybe<Scalars['Boolean']['input']>;
  is_published: Scalars['Boolean']['input'];
  location?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  open_mic_link?: InputMaybe<Scalars['String']['input']>;
  order?: InputMaybe<Scalars['Int']['input']>;
  org_contact_number?: InputMaybe<Scalars['String']['input']>;
  organizer_name?: InputMaybe<Scalars['String']['input']>;
  seating_plan?: InputMaybe<Scalars['Upload']['input']>;
  start_booking_date?: InputMaybe<Scalars['DateTime']['input']>;
  start_date?: InputMaybe<Scalars['DateTime']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
};


export type MutationUpdateOrganizerArgs = {
  id: Scalars['ID']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  password?: InputMaybe<Scalars['String']['input']>;
};


export type MutationUpdateSpotlightArgs = {
  id: Scalars['Int']['input'];
  image?: InputMaybe<Scalars['Upload']['input']>;
  is_external?: InputMaybe<Scalars['Boolean']['input']>;
  order?: InputMaybe<Scalars['Int']['input']>;
  url?: InputMaybe<Scalars['String']['input']>;
};


export type MutationUpdateStaffArgs = {
  password?: InputMaybe<Scalars['String']['input']>;
  user_id: Scalars['ID']['input'];
  username?: InputMaybe<Scalars['String']['input']>;
};


export type MutationUpsertEventArtistArgs = {
  avatar?: InputMaybe<Scalars['Upload']['input']>;
  event_id: Scalars['ID']['input'];
  id?: InputMaybe<Scalars['ID']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  order?: InputMaybe<Scalars['Int']['input']>;
  social_link?: InputMaybe<Scalars['String']['input']>;
};


export type MutationUpsertSettingArgs = {
  key: Scalars['String']['input'];
  value?: InputMaybe<Scalars['String']['input']>;
};


export type MutationUserLoginArgs = {
  identifier: Scalars['String']['input'];
  password: Scalars['String']['input'];
};


export type MutationUserRequestRefundArgs = {
  event_id: Scalars['ID']['input'];
  phone_number: Scalars['String']['input'];
};

export type MyEventsPaginator = {
  __typename?: 'MyEventsPaginator';
  data?: Maybe<Array<Event>>;
  paginatorInfo: AppPaginatorInfo;
};

/** Allows ordering a list of records. */
export type OrderByClause = {
  /** The column that is used for ordering. */
  column: Scalars['String']['input'];
  /** The direction that is used for ordering. */
  order: SortOrder;
};

/** Aggregate functions when ordering by a relation without specifying a column. */
export enum OrderByRelationAggregateFunction {
  /** Amount of items. */
  Count = 'COUNT'
}

/** Aggregate functions when ordering by a relation that may specify a column. */
export enum OrderByRelationWithColumnAggregateFunction {
  /** Average. */
  Avg = 'AVG',
  /** Amount of items. */
  Count = 'COUNT',
  /** Maximum. */
  Max = 'MAX',
  /** Minimum. */
  Min = 'MIN',
  /** Sum. */
  Sum = 'SUM'
}

/** Response of 3rd party payment */
export type OrderResponse = {
  __typename?: 'OrderResponse';
  goto_url?: Maybe<Scalars['String']['output']>;
  mobile_number: Scalars['String']['output'];
  order_id: Scalars['String']['output'];
  payment_id?: Maybe<Scalars['String']['output']>;
  /** provider used for creating the order. razorpay | phonepe | sgpg */
  provider_type: Scalars['String']['output'];
  username: Scalars['String']['output'];
};

export type OrganizerListPaginator = {
  __typename?: 'OrganizerListPaginator';
  currentPage: Scalars['Int']['output'];
  hasMorePages: Scalars['Boolean']['output'];
  organizers?: Maybe<Array<User>>;
  total: Scalars['Int']['output'];
};

export type OrganizerOnBoarding = {
  __typename?: 'OrganizerOnBoarding';
  address?: Maybe<Scalars['String']['output']>;
  email: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  mobile_number: Scalars['String']['output'];
  name: Scalars['String']['output'];
};

export type OtpResponse = {
  __typename?: 'OtpResponse';
  otp_id: Scalars['ID']['output'];
};

export type PpResponse = {
  __typename?: 'PPResponse';
  data?: Maybe<Scalars['String']['output']>;
};

/** Information about pagination using a fully featured paginator. */
export type PaginatorInfo = {
  __typename?: 'PaginatorInfo';
  /** Number of items in the current page. */
  count: Scalars['Int']['output'];
  /** Index of the current page. */
  currentPage: Scalars['Int']['output'];
  /** Index of the first item in the current page. */
  firstItem?: Maybe<Scalars['Int']['output']>;
  /** Are there more pages after this one? */
  hasMorePages: Scalars['Boolean']['output'];
  /** Index of the last item in the current page. */
  lastItem?: Maybe<Scalars['Int']['output']>;
  /** Index of the last available page. */
  lastPage: Scalars['Int']['output'];
  /** Number of items per page. */
  perPage: Scalars['Int']['output'];
  /** Number of total available items. */
  total: Scalars['Int']['output'];
};

export type PaymentOrder = {
  __typename?: 'PaymentOrder';
  id: Scalars['ID']['output'];
  paymentable?: Maybe<PaymentableOrder>;
  paymentable_id: Scalars['ID']['output'];
  paymentable_type: Scalars['String']['output'];
};

export type PaymentableOrder = PhonePePaymentOrder | RzpayPaymentOrder | SgpgPaymentOrder;

export type PhonePePaymentOrder = {
  __typename?: 'PhonePePaymentOrder';
  error_detail?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  order_id: Scalars['String']['output'];
  payment_id?: Maybe<Scalars['String']['output']>;
  payment_instrument_type?: Maybe<Scalars['String']['output']>;
  refund_amount?: Maybe<Scalars['String']['output']>;
  refund_error_detail?: Maybe<Scalars['String']['output']>;
  refund_status?: Maybe<Scalars['String']['output']>;
  refund_transaction_id?: Maybe<Scalars['String']['output']>;
  status: Scalars['String']['output'];
  transaction_id?: Maybe<Scalars['String']['output']>;
};

/** Indicates what fields are available at the top level of a query operation. */
export type Query = {
  __typename?: 'Query';
  /** Get all admin events */
  adminEvents?: Maybe<Array<Maybe<Event>>>;
  admins: UserPaginator;
  checkPhonePeHealth?: Maybe<PpResponse>;
  checkTransactionStatus?: Maybe<PaymentOrder>;
  /** Get event attendees */
  eventAttendees?: Maybe<EventAttendeesResponse>;
  eventById?: Maybe<Event>;
  eventBySlug?: Maybe<Event>;
  events: EventPaginator;
  generateRawReports: GenericResponse;
  getConvenienceFee?: Maybe<ConvenienceFee>;
  /** for admin only */
  getCouponsForEvent?: Maybe<Array<Maybe<Coupon>>>;
  getEventTicketPurchase: EventTicketPurchasePaginator;
  getEvents: GetEventsPaginator;
  /** Get guest list for event id for auth admin */
  getGuestList: GuestListResponse;
  getInvitees: EventTicketPurchasePaginator;
  getMe?: Maybe<AuthResponse>;
  getPastEvents: EventPaginator;
  getRemainingSeatsForSeatedEvent?: Maybe<Array<Scalars['String']['output']>>;
  getSeatsForSportsEvent: SeatsForSportEvent;
  getSettingByKey?: Maybe<AppSetting>;
  getSpotlightList?: Maybe<Array<Spotlight>>;
  /** Get staffs for currently logged in 'Admin' */
  getStaffs?: Maybe<Array<Maybe<User>>>;
  getTicketPurchaseDetail?: Maybe<TicketPurchaseDetail>;
  getTicketStatsForEvent?: Maybe<TicketStatusResponse>;
  getTicketTypesForEvent?: Maybe<Event>;
  isTicketDownloadable?: Maybe<TicketDownloadableResponse>;
  myEvents: MyEventsPaginator;
  organizerList?: Maybe<OrganizerListPaginator>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryAdminsArgs = {
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
  username?: InputMaybe<Scalars['String']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryCheckTransactionStatusArgs = {
  merchant_trnx_id: Scalars['String']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryEventAttendeesArgs = {
  event_id: Scalars['ID']['input'];
  first?: InputMaybe<Scalars['Int']['input']>;
  mobile_number?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  payment_status?: InputMaybe<Scalars['String']['input']>;
  ticket_type_id?: InputMaybe<Scalars['ID']['input']>;
  username?: InputMaybe<Scalars['String']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryEventByIdArgs = {
  id: Scalars['ID']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryEventBySlugArgs = {
  slug: Scalars['String']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryEventsArgs = {
  current_datetime: Scalars['DateTime']['input'];
  first?: Scalars['Int']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  type: Scalars['String']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGenerateRawReportsArgs = {
  file_name: Scalars['String']['input'];
  q: Scalars['String']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetConvenienceFeeArgs = {
  ticket_type_id: Scalars['ID']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetCouponsForEventArgs = {
  event_id: Scalars['ID']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetEventTicketPurchaseArgs = {
  first: Scalars['Int']['input'];
  mobile_number?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  username?: InputMaybe<Scalars['String']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetEventsArgs = {
  current_datetime?: InputMaybe<Scalars['DateTime']['input']>;
  event_types?: InputMaybe<Array<Scalars['String']['input']>>;
  first: Scalars['Int']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  sort_order?: InputMaybe<Scalars['String']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetGuestListArgs = {
  event_id: Scalars['ID']['input'];
  first: Scalars['Int']['input'];
  is_offline?: InputMaybe<Scalars['Boolean']['input']>;
  mobile_number?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  ticket_type_id?: InputMaybe<Scalars['ID']['input']>;
  username?: InputMaybe<Scalars['String']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetInviteesArgs = {
  event_id: Scalars['ID']['input'];
  first: Scalars['Int']['input'];
  mobile_number?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  username?: InputMaybe<Scalars['String']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetPastEventsArgs = {
  current_datetime: Scalars['DateTime']['input'];
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetRemainingSeatsForSeatedEventArgs = {
  event_id: Scalars['ID']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetSeatsForSportsEventArgs = {
  ticket_type_id: Scalars['Int']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetSettingByKeyArgs = {
  key?: InputMaybe<Scalars['String']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetTicketPurchaseDetailArgs = {
  event_id?: InputMaybe<Scalars['Int']['input']>;
  qrcode: Scalars['String']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetTicketStatsForEventArgs = {
  event_id: Scalars['ID']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetTicketTypesForEventArgs = {
  id: Scalars['ID']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryIsTicketDownloadableArgs = {
  order_id: Scalars['String']['input'];
  payment_id: Scalars['String']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryMyEventsArgs = {
  first: Scalars['Int']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  sort_order?: InputMaybe<Scalars['String']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryOrganizerListArgs = {
  first?: InputMaybe<Scalars['Int']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
};

export type RedeemCouponResponse = {
  __typename?: 'RedeemCouponResponse';
  /** discount percentage */
  amount: Scalars['Float']['output'];
  /** coupon hash */
  rc: Scalars['String']['output'];
  /** remaining coupon */
  remaining_cp: Scalars['Int']['output'];
};

export type RefundResponse = {
  __typename?: 'RefundResponse';
  reason?: Maybe<Scalars['String']['output']>;
  status: Scalars['String']['output'];
};

export type RzpayPaymentOrder = {
  __typename?: 'RzpayPaymentOrder';
  id: Scalars['ID']['output'];
  order_id: Scalars['String']['output'];
  payment_id?: Maybe<Scalars['String']['output']>;
  refund_amount?: Maybe<Scalars['Float']['output']>;
  refund_error_detail?: Maybe<Scalars['String']['output']>;
  refund_id?: Maybe<Scalars['String']['output']>;
  refund_status?: Maybe<Scalars['String']['output']>;
  status: Scalars['String']['output'];
};

export type SgpgPaymentOrder = {
  __typename?: 'SGPGPaymentOrder';
  error_detail?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  order_id: Scalars['String']['output'];
  payment_id?: Maybe<Scalars['String']['output']>;
  payment_instrument_type?: Maybe<Scalars['String']['output']>;
  refund_amount?: Maybe<Scalars['String']['output']>;
  refund_error_detail?: Maybe<Scalars['String']['output']>;
  refund_status?: Maybe<Scalars['String']['output']>;
  refund_transaction_id?: Maybe<Scalars['String']['output']>;
  status: Scalars['String']['output'];
  transaction_id?: Maybe<Scalars['String']['output']>;
};

export type SeatsForSportEvent = {
  __typename?: 'SeatsForSportEvent';
  all_seats?: Maybe<Array<Scalars['Int']['output']>>;
  available_seats?: Maybe<Array<Scalars['Int']['output']>>;
  /** cv for a single ticket calculated off of single ticket price */
  convenience_fee: Scalars['Float']['output'];
  /** price for single ticket of a particular ticket type */
  price: Scalars['Float']['output'];
};

/** Directions for ordering a list of records. */
export enum SortOrder {
  /** Sort records in ascending order. */
  Asc = 'ASC',
  /** Sort records in descending order. */
  Desc = 'DESC'
}

export type SportGuestList = {
  __typename?: 'SportGuestList';
  data?: Maybe<Array<SportTicketPurchase>>;
  paginatorInfo: AppPaginatorInfo;
};

export type SportTicketPurchase = {
  __typename?: 'SportTicketPurchase';
  amount_paid: Scalars['Float']['output'];
  /** Organizers contact number */
  contact_number: Scalars['String']['output'];
  convenience_fee?: Maybe<Scalars['Float']['output']>;
  created_at: Scalars['DateTime']['output'];
  event?: Maybe<Event>;
  eventTicketType?: Maybe<SportTicketType>;
  event_id: Scalars['ID']['output'];
  id: Scalars['ID']['output'];
  is_gift?: Maybe<Scalars['Boolean']['output']>;
  is_offline: Scalars['Boolean']['output'];
  /** User mobile number */
  mobile_number?: Maybe<Scalars['String']['output']>;
  number_of_tickets: Scalars['Int']['output'];
  paymentOrder?: Maybe<PaymentOrder>;
  /** Ticket payment status. Will be set according to status received from 3rd party payment provider Note: this is not 3rd party payment provider status */
  payment_status: Scalars['String']['output'];
  pdf_media_id?: Maybe<Scalars['String']['output']>;
  qrcode?: Maybe<Scalars['String']['output']>;
  recipient_message?: Maybe<Scalars['String']['output']>;
  recipient_name?: Maybe<Scalars['String']['output']>;
  recipient_phone?: Maybe<Scalars['String']['output']>;
  scanned_at?: Maybe<Scalars['DateTime']['output']>;
  scanned_by?: Maybe<Scalars['ID']['output']>;
  /** general does not have seat number */
  seat_numbers: Array<Scalars['Int']['output']>;
  should_consider_seat: Scalars['Boolean']['output'];
  ticket_path?: Maybe<Scalars['String']['output']>;
  tickets_scanned_count: Scalars['Int']['output'];
  tip_amount?: Maybe<Scalars['Float']['output']>;
  tip_message?: Maybe<Scalars['String']['output']>;
  username?: Maybe<Scalars['String']['output']>;
  whatsapp_sent_status?: Maybe<Scalars['String']['output']>;
};

export type SportTicketType = {
  __typename?: 'SportTicketType';
  end_seat_number: Scalars['Int']['output'];
  event?: Maybe<Event>;
  id: Scalars['ID']['output'];
  is_convenience_fee_incl?: Maybe<Scalars['Boolean']['output']>;
  is_gst_incl?: Maybe<Scalars['Boolean']['output']>;
  price: Scalars['String']['output'];
  should_consider_seat: Scalars['Boolean']['output'];
  slug: Scalars['String']['output'];
  start_seat_number: Scalars['Int']['output'];
  ticket_type: Scalars['String']['output'];
};

export type Spotlight = {
  __typename?: 'Spotlight';
  app_image_id: Scalars['Int']['output'];
  id: Scalars['ID']['output'];
  image: AppImage;
  /** if true, should open link(url) in new tab */
  is_external?: Maybe<Scalars['Boolean']['output']>;
  /** ascending order */
  order: Scalars['Int']['output'];
  /** optional url for when spotlight is clicked */
  url?: Maybe<Scalars['String']['output']>;
};

export type SubmitQrCodeResponse = {
  __typename?: 'SubmitQRCodeResponse';
  error?: Maybe<Scalars['String']['output']>;
};

export type SuccessResponse = {
  __typename?: 'SuccessResponse';
  message: Scalars['String']['output'];
};

export type TicketDownloadableResponse = {
  __typename?: 'TicketDownloadableResponse';
  amount: Scalars['Float']['output'];
  ticket_path?: Maybe<Scalars['String']['output']>;
};

export type TicketPurchaseDetail = EventTicketPurchase | SportTicketPurchase;

export type TicketStatusResponse = {
  __typename?: 'TicketStatusResponse';
  ticket_type_stats?: Maybe<Array<Maybe<TicketTypeStats>>>;
  /** Total amount excluding convenience fee */
  total_amount?: Maybe<Scalars['Float']['output']>;
  total_tickets_sold?: Maybe<Scalars['Int']['output']>;
  total_tip_amount?: Maybe<Scalars['Float']['output']>;
};

export type TicketTypeStats = {
  __typename?: 'TicketTypeStats';
  num_of_tickets: Scalars['Int']['output'];
  ticket_type: Scalars['String']['output'];
};

export type TicketTypeStatus = {
  __typename?: 'TicketTypeStatus';
  ticket_type: Scalars['String']['output'];
  ticket_type_id: Scalars['ID']['output'];
  /** possible values : `sold_out` | `filling_fast` | `few_left` | `available` */
  value: Scalars['String']['output'];
};

/** Specify if you want to include or exclude trashed results from a query. */
export enum Trashed {
  /** Only return trashed results. */
  Only = 'ONLY',
  /** Return both trashed and non-trashed results. */
  With = 'WITH',
  /** Only return non-trashed results. */
  Without = 'WITHOUT'
}

/** Account of a person who utilizes this application. */
export type User = {
  __typename?: 'User';
  /** When the account was created. */
  created_at: Scalars['DateTime']['output'];
  event?: Maybe<Event>;
  event_id?: Maybe<Scalars['ID']['output']>;
  events?: Maybe<Array<Maybe<Event>>>;
  /** Unique primary key. */
  id: Scalars['ID']['output'];
  identifier: Scalars['String']['output'];
  role: Scalars['String']['output'];
  rzpay_account_id?: Maybe<Scalars['String']['output']>;
  /** When the account was last updated. */
  updated_at: Scalars['DateTime']['output'];
  verified_at?: Maybe<Scalars['DateTime']['output']>;
};

/** A paginated list of User items. */
export type UserPaginator = {
  __typename?: 'UserPaginator';
  /** A list of User items. */
  data: Array<User>;
  /** Pagination information about the list of items. */
  paginatorInfo: PaginatorInfo;
};

export type TicketStatsQueryVariables = Exact<{
  eventID: Scalars['ID']['input'];
}>;


export type TicketStatsQuery = { __typename?: 'Query', getTicketStatsForEvent?: { __typename?: 'TicketStatusResponse', total_amount?: number | null, total_tip_amount?: number | null, total_tickets_sold?: number | null, ticket_type_stats?: Array<{ __typename?: 'TicketTypeStats', num_of_tickets: number, ticket_type: string } | null> | null } | null };

export type AddSpotlightMutationVariables = Exact<{
  image: Scalars['Upload']['input'];
  url?: InputMaybe<Scalars['String']['input']>;
  isExternal?: InputMaybe<Scalars['Boolean']['input']>;
  order?: InputMaybe<Scalars['Int']['input']>;
}>;


export type AddSpotlightMutation = { __typename?: 'Mutation', addSpotlight: { __typename?: 'GenericResponse', message: string } };

export type CreateSportEventPaymentMutationVariables = Exact<{
  ticketTypeId: Scalars['Int']['input'];
  mobileNumber: Scalars['String']['input'];
  numberOfTickets?: InputMaybe<Scalars['Int']['input']>;
  username: Scalars['String']['input'];
  seatNumbers: Array<Scalars['Int']['input']> | Scalars['Int']['input'];
  tipAmount?: InputMaybe<Scalars['Float']['input']>;
  tipMEssage?: InputMaybe<Scalars['String']['input']>;
}>;


export type CreateSportEventPaymentMutation = { __typename?: 'Mutation', createSportEventPayment: { __typename?: 'OrderResponse', order_id: string, payment_id?: string | null, provider_type: string, goto_url?: string | null, username: string, mobile_number: string } };

export type CreateEventMutationVariables = Exact<{
  name: Scalars['String']['input'];
  description?: InputMaybe<Scalars['String']['input']>;
  location: Scalars['String']['input'];
  eventTicketType?: InputMaybe<Array<CreateEventTicketTypeInput> | CreateEventTicketTypeInput>;
  eventImages: Array<InputMaybe<Scalars['Upload']['input']>> | InputMaybe<Scalars['Upload']['input']>;
  startDate: Scalars['DateTime']['input'];
  endDate: Scalars['DateTime']['input'];
  startBookingDate?: InputMaybe<Scalars['DateTime']['input']>;
  address: Scalars['String']['input'];
  orgName: Scalars['String']['input'];
  orgContactNumber: Scalars['String']['input'];
  isPublished: Scalars['Boolean']['input'];
  isPrivateEvent?: InputMaybe<Scalars['Boolean']['input']>;
  faq?: InputMaybe<Scalars['String']['input']>;
  artists?: InputMaybe<Array<InputMaybe<EventArtistsInput>> | InputMaybe<EventArtistsInput>>;
  openMicLink?: InputMaybe<Scalars['String']['input']>;
  city: Scalars['String']['input'];
  eventType?: InputMaybe<EventType>;
}>;


export type CreateEventMutation = { __typename?: 'Mutation', createEvent?: { __typename?: 'Event', id: string, is_published: boolean } | null };

export type CreatePaymentMutationVariables = Exact<{
  eventTicketTypeId: Scalars['ID']['input'];
  mobileNumber: Scalars['String']['input'];
  username: Scalars['String']['input'];
  numberOfTickets: Scalars['Int']['input'];
  couponHash?: InputMaybe<Scalars['String']['input']>;
  isGift?: InputMaybe<Scalars['Boolean']['input']>;
  recipientName?: InputMaybe<Scalars['String']['input']>;
  recipientPhone?: InputMaybe<Scalars['String']['input']>;
  recipientMessage?: InputMaybe<Scalars['String']['input']>;
  tipAmount?: InputMaybe<Scalars['Float']['input']>;
  tipMessage?: InputMaybe<Scalars['String']['input']>;
}>;


export type CreatePaymentMutation = { __typename?: 'Mutation', createPayment?: { __typename?: 'OrderResponse', order_id: string, payment_id?: string | null, provider_type: string, goto_url?: string | null, mobile_number: string, username: string } | null };

export type DeleteSpotlightMutationVariables = Exact<{
  id: Scalars['Int']['input'];
}>;


export type DeleteSpotlightMutation = { __typename?: 'Mutation', deleteSpotlight: { __typename?: 'GenericResponse', message: string } };

export type LogoutMutationVariables = Exact<{ [key: string]: never; }>;


export type LogoutMutation = { __typename?: 'Mutation', logout?: { __typename?: 'SuccessResponse', message: string } | null };

export type RedeemCouponMutationVariables = Exact<{
  coupon: Scalars['String']['input'];
  eventId: Scalars['ID']['input'];
}>;


export type RedeemCouponMutation = { __typename?: 'Mutation', redeemCoupon?: { __typename?: 'RedeemCouponResponse', rc: string, amount: number } | null };

export type UpdateSpotlightMutationVariables = Exact<{
  id: Scalars['Int']['input'];
  image?: InputMaybe<Scalars['Upload']['input']>;
  url?: InputMaybe<Scalars['String']['input']>;
  order?: InputMaybe<Scalars['Int']['input']>;
  isExternal?: InputMaybe<Scalars['Boolean']['input']>;
}>;


export type UpdateSpotlightMutation = { __typename?: 'Mutation', updateSpotlight: { __typename?: 'GenericResponse', message: string } };

export type UpdateEventMutationVariables = Exact<{
  id: Scalars['ID']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  location?: InputMaybe<Scalars['String']['input']>;
  eventImages?: InputMaybe<Array<InputMaybe<Scalars['Upload']['input']>> | InputMaybe<Scalars['Upload']['input']>>;
  address?: InputMaybe<Scalars['String']['input']>;
  orgName?: InputMaybe<Scalars['String']['input']>;
  orgContactNumber?: InputMaybe<Scalars['String']['input']>;
  isPublished: Scalars['Boolean']['input'];
  isPrivateEvent?: InputMaybe<Scalars['Boolean']['input']>;
  startDate?: InputMaybe<Scalars['DateTime']['input']>;
  endDate?: InputMaybe<Scalars['DateTime']['input']>;
  startBookingDate?: InputMaybe<Scalars['DateTime']['input']>;
  openMicLink?: InputMaybe<Scalars['String']['input']>;
  seatingPlan?: InputMaybe<Scalars['Upload']['input']>;
  city?: InputMaybe<Scalars['String']['input']>;
  faq?: InputMaybe<Scalars['String']['input']>;
}>;


export type UpdateEventMutation = { __typename?: 'Mutation', updateEvent?: { __typename?: 'Event', id: string } | null };

export type UpsertEventArtistMutationVariables = Exact<{
  id?: InputMaybe<Scalars['ID']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  avatar?: InputMaybe<Scalars['Upload']['input']>;
  order?: InputMaybe<Scalars['Int']['input']>;
  eventId: Scalars['ID']['input'];
  social_link?: InputMaybe<Scalars['String']['input']>;
}>;


export type UpsertEventArtistMutation = { __typename?: 'Mutation', upsertEventArtist?: { __typename?: 'EventArtist', id: string } | null };

export type EventBySlugQueryVariables = Exact<{
  slug: Scalars['String']['input'];
}>;


export type EventBySlugQuery = { __typename?: 'Query', eventBySlug?: { __typename?: 'Event', id: string, name: string, description?: string | null, type: string, location?: string | null, slug: string, start_date: any, end_date: any, start_booking_date?: any | null, address: string, org_contact_number: string, organizer_name: string, faq?: string | null, open_mic_link?: string | null, eventTicketTypes?: Array<{ __typename?: 'EventTicketType', id: string, ticket_type: string, price: string, maximum_capacity: number, status: string, is_convenience_fee_incl: boolean, is_gst_incl: boolean } | null> | null, seatingPlan?: { __typename?: 'AppImage', id: string, path: string } | null, images?: Array<{ __typename?: 'AppImage', id: string, path: string, hash?: string | null, width?: number | null, height?: number | null } | null> | null, artists?: Array<{ __typename?: 'EventArtist', id: string, name: string, avatar: string, social_link?: string | null } | null> | null, coupons?: Array<{ __typename?: 'Coupon', id: string } | null> | null } | null };

export type EventsQueryVariables = Exact<{
  page: Scalars['Int']['input'];
  currentDatetime: Scalars['DateTime']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  event_types?: InputMaybe<Array<Scalars['String']['input']> | Scalars['String']['input']>;
  first: Scalars['Int']['input'];
}>;


export type EventsQuery = { __typename?: 'Query', getEvents: { __typename?: 'GetEventsPaginator', data?: Array<{ __typename?: 'Event', id: string, name: string, start_date: any, end_date: any, type: string, slug: string, start_booking_date?: any | null, open_mic_link?: string | null, city?: string | null, images?: Array<{ __typename?: 'AppImage', id: string, path: string, hash?: string | null, width?: number | null, height?: number | null } | null> | null, eventTicketTypes?: Array<{ __typename?: 'EventTicketType', price: string, ticket_type: string } | null> | null }> | null, paginatorInfo: { __typename?: 'AppPaginatorInfo', lastPage: number, hasMorePages: boolean, currentPage: number } } };

export type EventsForSuperAdminQueryVariables = Exact<{
  page: Scalars['Int']['input'];
  currentDatetime: Scalars['DateTime']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  first: Scalars['Int']['input'];
  type: Scalars['String']['input'];
}>;


export type EventsForSuperAdminQuery = { __typename?: 'Query', events: { __typename?: 'EventPaginator', data: Array<{ __typename?: 'Event', id: string, name: string }> } };

export type GetEventsQueryVariables = Exact<{
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  eventTypes?: InputMaybe<Array<Scalars['String']['input']> | Scalars['String']['input']>;
  sortOrder?: InputMaybe<Scalars['String']['input']>;
}>;


export type GetEventsQuery = { __typename?: 'Query', getEvents: { __typename?: 'GetEventsPaginator', data?: Array<{ __typename?: 'Event', id: string, name: string, type: string, start_date: any, end_date: any, slug: string, start_booking_date?: any | null, open_mic_link?: string | null, city?: string | null, images?: Array<{ __typename?: 'AppImage', id: string, path: string, hash?: string | null, width?: number | null, height?: number | null } | null> | null, eventTicketTypes?: Array<{ __typename?: 'EventTicketType', price: string, ticket_type: string } | null> | null, sportTicketTypes?: Array<{ __typename?: 'SportTicketType', price: string, ticket_type: string } | null> | null }> | null, paginatorInfo: { __typename?: 'AppPaginatorInfo', hasMorePages: boolean, currentPage: number, lastPage: number } } };

export type GetSeatsForSportsEventQueryVariables = Exact<{
  ticketTypeId: Scalars['Int']['input'];
}>;


export type GetSeatsForSportsEventQuery = { __typename?: 'Query', getSeatsForSportsEvent: { __typename?: 'SeatsForSportEvent', all_seats?: Array<number> | null, available_seats?: Array<number> | null, price: number, convenience_fee: number } };

export type GetSpotlightListQueryVariables = Exact<{ [key: string]: never; }>;


export type GetSpotlightListQuery = { __typename?: 'Query', getSpotlightList?: Array<{ __typename?: 'Spotlight', id: string, app_image_id: number, url?: string | null, is_external?: boolean | null, order: number, image: { __typename?: 'AppImage', id: string, path: string, hash?: string | null } }> | null };

export type GetConvenienceFeeQueryVariables = Exact<{
  ticketTypeId: Scalars['ID']['input'];
}>;


export type GetConvenienceFeeQuery = { __typename?: 'Query', getConvenienceFee?: { __typename?: 'ConvenienceFee', amount: number } | null };

export type GetCouponsForEventQueryVariables = Exact<{
  eventId: Scalars['ID']['input'];
}>;


export type GetCouponsForEventQuery = { __typename?: 'Query', getCouponsForEvent?: Array<{ __typename?: 'Coupon', id: string, name: string, expiry_date: any, max_usage?: number | null, is_published: boolean, discount_percent: number, coupon_usage_count: number } | null> | null };

export type EventByIdQueryVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type EventByIdQuery = { __typename?: 'Query', eventById?: { __typename?: 'Event', id: string, name: string, description?: string | null, location?: string | null, address: string, org_contact_number: string, is_published: boolean, is_private_event: boolean, organizer_name: string, slug: string, start_date: any, end_date: any, start_booking_date?: any | null, open_mic_link?: string | null, city?: string | null, faq?: string | null, images?: Array<{ __typename?: 'AppImage', id: string, path: string, hash?: string | null, width?: number | null, height?: number | null } | null> | null, seatingPlan?: { __typename?: 'AppImage', id: string, path: string, hash?: string | null } | null, eventTicketTypes?: Array<{ __typename?: 'EventTicketType', id: string, ticket_type: string, price: string, maximum_capacity: number } | null> | null, artists?: Array<{ __typename?: 'EventArtist', id: string, name: string, avatar: string, social_link?: string | null, order?: number | null } | null> | null } | null };

export type GetGuestListQueryVariables = Exact<{
  eventId: Scalars['ID']['input'];
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
  userName?: InputMaybe<Scalars['String']['input']>;
  mobileNumber?: InputMaybe<Scalars['String']['input']>;
  ticketTypeId?: InputMaybe<Scalars['ID']['input']>;
}>;


export type GetGuestListQuery = { __typename?: 'Query', getGuestList: { __typename: 'GeneralGuestList', data?: Array<{ __typename?: 'EventTicketPurchase', id: string, username?: string | null, mobile_number?: string | null, number_of_tickets: number, scanned_at?: any | null, tip_amount?: number | null, tip_message?: string | null, paymentOrder?: { __typename?: 'PaymentOrder', paymentable?: { __typename: 'PhonePePaymentOrder', order_id: string, payment_id?: string | null } | { __typename: 'RzpayPaymentOrder', order_id: string, payment_id?: string | null } | { __typename: 'SGPGPaymentOrder' } | null } | null, eventTicketType?: { __typename?: 'EventTicketType', id: string, ticket_type: string } | null }> | null, paginatorInfo: { __typename?: 'AppPaginatorInfo', lastPage: number, total: number, currentPage: number, hasMorePages: boolean } } | { __typename: 'SportGuestList', data?: Array<{ __typename?: 'SportTicketPurchase', id: string, username?: string | null, mobile_number?: string | null, number_of_tickets: number, seat_numbers: Array<number>, scanned_at?: any | null, tip_amount?: number | null, tip_message?: string | null, paymentOrder?: { __typename?: 'PaymentOrder', paymentable?: { __typename: 'PhonePePaymentOrder', order_id: string, payment_id?: string | null } | { __typename: 'RzpayPaymentOrder', order_id: string, payment_id?: string | null } | { __typename: 'SGPGPaymentOrder' } | null } | null, eventTicketType?: { __typename?: 'SportTicketType', id: string, ticket_type: string, slug: string } | null }> | null, paginatorInfo: { __typename?: 'AppPaginatorInfo', lastPage: number, total: number, currentPage: number, hasMorePages: boolean } } };

export type GetInviteesQueryVariables = Exact<{
  username?: InputMaybe<Scalars['String']['input']>;
  mobileNumber?: InputMaybe<Scalars['String']['input']>;
  eventId: Scalars['ID']['input'];
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
}>;


export type GetInviteesQuery = { __typename?: 'Query', getInvitees: { __typename?: 'EventTicketPurchasePaginator', data: Array<{ __typename?: 'EventTicketPurchase', id: string, username?: string | null, mobile_number?: string | null, whatsapp_sent_status?: string | null, number_of_tickets: number, scanned_at?: any | null, paymentOrder?: { __typename?: 'PaymentOrder', paymentable?: { __typename: 'PhonePePaymentOrder', order_id: string, payment_id?: string | null } | { __typename: 'RzpayPaymentOrder', order_id: string, payment_id?: string | null } | { __typename: 'SGPGPaymentOrder' } | null } | null }>, paginatorInfo: { __typename?: 'PaginatorInfo', lastPage: number, total: number, hasMorePages: boolean, currentPage: number } } };

export type GetMeQueryVariables = Exact<{ [key: string]: never; }>;


export type GetMeQuery = { __typename?: 'Query', getMe?: { __typename?: 'AuthResponse', token?: string | null, user?: { __typename?: 'User', id: string, role: string, identifier: string } | null } | null };

export type MyEventsQueryVariables = Exact<{
  page: Scalars['Int']['input'];
  first: Scalars['Int']['input'];
}>;


export type MyEventsQuery = { __typename?: 'Query', myEvents: { __typename?: 'MyEventsPaginator', data?: Array<{ __typename?: 'Event', id: string, name: string, description?: string | null, location?: string | null, slug: string, start_date: any, end_date: any, address: string, org_contact_number: string, is_published: boolean, is_private_event: boolean, eventTicketTypes?: Array<{ __typename?: 'EventTicketType', id: string, ticket_type: string, price: string, maximum_capacity: number } | null> | null, images?: Array<{ __typename?: 'AppImage', id: string, path: string, hash?: string | null, width?: number | null, height?: number | null } | null> | null }> | null, paginatorInfo: { __typename?: 'AppPaginatorInfo', lastPage: number, hasMorePages: boolean, currentPage: number } } };

export type GetOrganizersQueryVariables = Exact<{
  page?: InputMaybe<Scalars['Int']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
}>;


export type GetOrganizersQuery = { __typename?: 'Query', organizerList?: { __typename?: 'OrganizerListPaginator', hasMorePages: boolean, currentPage: number, total: number, organizers?: Array<{ __typename?: 'User', id: string, identifier: string }> | null } | null };

export type PastEventsQueryVariables = Exact<{
  first: Scalars['Int']['input'];
  currentDatetime: Scalars['DateTime']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
}>;


export type PastEventsQuery = { __typename?: 'Query', getPastEvents: { __typename?: 'EventPaginator', data: Array<{ __typename?: 'Event', id: string, name: string, type: string, end_date: any, slug: string, images?: Array<{ __typename?: 'AppImage', id: string, path: string, hash?: string | null, width?: number | null, height?: number | null } | null> | null }>, paginatorInfo: { __typename?: 'PaginatorInfo', hasMorePages: boolean, currentPage: number, lastPage: number } } };

export type SportsBySlugQueryVariables = Exact<{
  slug: Scalars['String']['input'];
}>;


export type SportsBySlugQuery = { __typename?: 'Query', eventBySlug?: { __typename?: 'Event', id: string, name: string, description?: string | null, location?: string | null, type: string, slug: string, start_date: any, end_date: any, start_booking_date?: any | null, address: string, org_contact_number: string, organizer_name: string, faq?: string | null, open_mic_link?: string | null, sportTicketTypes?: Array<{ __typename?: 'SportTicketType', id: string, price: string, ticket_type: string, start_seat_number: number, end_seat_number: number, slug: string } | null> | null, seatingPlan?: { __typename?: 'AppImage', id: string, path: string } | null, images?: Array<{ __typename?: 'AppImage', id: string, path: string, hash?: string | null, width?: number | null, height?: number | null } | null> | null, artists?: Array<{ __typename?: 'EventArtist', id: string, name: string, avatar: string, social_link?: string | null } | null> | null, coupons?: Array<{ __typename?: 'Coupon', id: string } | null> | null } | null };

export type SportsQueryVariables = Exact<{
  page: Scalars['Int']['input'];
  currentDatetime: Scalars['DateTime']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  type: Scalars['String']['input'];
}>;


export type SportsQuery = { __typename?: 'Query', events: { __typename?: 'EventPaginator', data: Array<{ __typename?: 'Event', id: string, name: string, start_date: any, slug: string, start_booking_date?: any | null, open_mic_link?: string | null, city?: string | null, images?: Array<{ __typename?: 'AppImage', id: string, path: string, hash?: string | null, width?: number | null, height?: number | null } | null> | null, sportTicketTypes?: Array<{ __typename?: 'SportTicketType', price: string, ticket_type: string, start_seat_number: number, end_seat_number: number } | null> | null }>, paginatorInfo: { __typename?: 'PaginatorInfo', lastPage: number } } };

export type AddStaffMutationVariables = Exact<{
  username: Scalars['String']['input'];
  password: Scalars['String']['input'];
}>;


export type AddStaffMutation = { __typename?: 'Mutation', addStaff?: { __typename?: 'User', id: string } | null };

export type GetStaffsQueryVariables = Exact<{ [key: string]: never; }>;


export type GetStaffsQuery = { __typename?: 'Query', getStaffs?: Array<{ __typename?: 'User', id: string, identifier: string, role: string } | null> | null };

export type DeleteStaffMutationVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type DeleteStaffMutation = { __typename?: 'Mutation', deleteStaff?: { __typename?: 'SuccessResponse', message: string } | null };

export type CreateCouponMutationVariables = Exact<{
  data: Array<CreateCouponInput> | CreateCouponInput;
}>;


export type CreateCouponMutation = { __typename?: 'Mutation', createCoupon?: { __typename?: 'GenericResponse', message: string } | null };

export type DeleteCouponMutationVariables = Exact<{
  couponId: Scalars['ID']['input'];
}>;


export type DeleteCouponMutation = { __typename?: 'Mutation', deleteCoupon?: { __typename?: 'Coupon', id: string } | null };

export type UpdateCouponMutationVariables = Exact<{
  couponId: Scalars['ID']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  expiry_date?: InputMaybe<Scalars['DateTime']['input']>;
  event_id?: InputMaybe<Scalars['ID']['input']>;
  discount_percent?: InputMaybe<Scalars['Float']['input']>;
  max_usage?: InputMaybe<Scalars['Int']['input']>;
  is_published?: InputMaybe<Scalars['Boolean']['input']>;
}>;


export type UpdateCouponMutation = { __typename?: 'Mutation', updateCoupon?: { __typename?: 'Coupon', id: string } | null };

export type AddInviteeMutationVariables = Exact<{
  eventId: Scalars['ID']['input'];
  username: Scalars['String']['input'];
  mobileNumber: Scalars['String']['input'];
  numberOfTickets: Scalars['Int']['input'];
}>;


export type AddInviteeMutation = { __typename?: 'Mutation', addInvitee?: { __typename?: 'EventTicketPurchase', id: string } | null };

export type DeleteEventMutationVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type DeleteEventMutation = { __typename?: 'Mutation', deleteEvent?: { __typename?: 'Event', id: string } | null };

export type DeleteImageByIdMutationVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type DeleteImageByIdMutation = { __typename?: 'Mutation', deleteImageById?: { __typename?: 'SuccessResponse', message: string } | null };

export type DeleteEventArtistMutationVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type DeleteEventArtistMutation = { __typename?: 'Mutation', deleteEventArtist?: { __typename?: 'SuccessResponse', message: string } | null };

export type TicketAvailableQueryVariables = Exact<{
  orderId: Scalars['String']['input'];
  paymentId: Scalars['String']['input'];
}>;


export type TicketAvailableQuery = { __typename?: 'Query', isTicketDownloadable?: { __typename?: 'TicketDownloadableResponse', ticket_path?: string | null, amount: number } | null };

export type GetRemainingSeatsForSeatedEventQueryVariables = Exact<{
  eventId: Scalars['ID']['input'];
}>;


export type GetRemainingSeatsForSeatedEventQuery = { __typename?: 'Query', getRemainingSeatsForSeatedEvent?: Array<string> | null };

export type OnBoardMutationVariables = Exact<{
  email: Scalars['String']['input'];
  name: Scalars['String']['input'];
  address?: InputMaybe<Scalars['String']['input']>;
  mobileNumber: Scalars['String']['input'];
}>;


export type OnBoardMutation = { __typename?: 'Mutation', onboardOrganizer?: { __typename?: 'OrganizerOnBoarding', id: string } | null };

export type UserRequestRefundMutationVariables = Exact<{
  eventId: Scalars['ID']['input'];
  phoneNumber: Scalars['String']['input'];
}>;


export type UserRequestRefundMutation = { __typename?: 'Mutation', userRequestRefund?: { __typename?: 'GenericResponse', message: string } | null };

export type SubmitQrCodeMutationVariables = Exact<{
  qrCode: Scalars['String']['input'];
}>;


export type SubmitQrCodeMutation = { __typename?: 'Mutation', submitQRCode?: { __typename?: 'SubmitQRCodeResponse', error?: string | null } | null };

export type StaffEventsQueryVariables = Exact<{ [key: string]: never; }>;


export type StaffEventsQuery = { __typename?: 'Query', adminEvents?: Array<{ __typename?: 'Event', id: string, name: string, eventTicketTypes?: Array<{ __typename?: 'EventTicketType', id: string, ticket_type: string, price: string, maximum_capacity: number } | null> | null } | null> | null };

export type UpsertSettingMutationVariables = Exact<{
  key: Scalars['String']['input'];
  value?: InputMaybe<Scalars['String']['input']>;
}>;


export type UpsertSettingMutation = { __typename?: 'Mutation', upsertSetting?: { __typename?: 'AppSetting', id: string } | null };

export type GetSettingByKeyQueryVariables = Exact<{
  key?: InputMaybe<Scalars['String']['input']>;
}>;


export type GetSettingByKeyQuery = { __typename?: 'Query', getSettingByKey?: { __typename?: 'AppSetting', id: string, key: string, value?: string | null } | null };

export type EventAttendeesQueryVariables = Exact<{
  eventID: Scalars['ID']['input'];
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
  username?: InputMaybe<Scalars['String']['input']>;
  mobileNumber?: InputMaybe<Scalars['String']['input']>;
  paymentStatus?: InputMaybe<Scalars['String']['input']>;
}>;


export type EventAttendeesQuery = { __typename?: 'Query', eventAttendees?: { __typename?: 'EventAttendeesResponse', data?: Array<{ __typename?: 'EventTicketPurchase', id: string, username?: string | null, mobile_number?: string | null, amount_paid: number, payment_status: string, number_of_tickets: number, ticket_path?: string | null, paymentOrder?: { __typename?: 'PaymentOrder', paymentable?: { __typename: 'PhonePePaymentOrder', id: string, order_id: string, status: string, refund_status?: string | null, refund_error_detail?: string | null } | { __typename: 'RzpayPaymentOrder', id: string, order_id: string, status: string, refund_status?: string | null, refund_error_detail?: string | null } | { __typename: 'SGPGPaymentOrder' } | null } | null } | null> | null, paginatorInfo?: { __typename?: 'EventAttendeesResponsePaginatorInfo', lastPage: number, total: number, currentPage: number } | null } | null };

export type IssueRefundMutationVariables = Exact<{
  amount: Scalars['Float']['input'];
  orderID: Scalars['String']['input'];
}>;


export type IssueRefundMutation = { __typename?: 'Mutation', issueRefund?: { __typename?: 'RefundResponse', status: string, reason?: string | null } | null };

export type AddOrganizerMutationVariables = Exact<{
  name: Scalars['String']['input'];
  password: Scalars['String']['input'];
}>;


export type AddOrganizerMutation = { __typename?: 'Mutation', addOrganizer?: { __typename?: 'SuccessResponse', message: string } | null };

export type UpdateOrganizerMutationVariables = Exact<{
  id: Scalars['ID']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  password?: InputMaybe<Scalars['String']['input']>;
}>;


export type UpdateOrganizerMutation = { __typename?: 'Mutation', updateOrganizer?: { __typename?: 'User', id: string } | null };

export type CheckPhonepeHealthQueryVariables = Exact<{ [key: string]: never; }>;


export type CheckPhonepeHealthQuery = { __typename?: 'Query', checkPhonePeHealth?: { __typename?: 'PPResponse', data?: string | null } | null };

export type NotifyUpcomingEventMutationVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type NotifyUpcomingEventMutation = { __typename?: 'Mutation', notifyUpcomingEvent?: { __typename?: 'Event', id: string } | null };

export type LoginMutationVariables = Exact<{
  identifier: Scalars['String']['input'];
  password: Scalars['String']['input'];
}>;


export type LoginMutation = { __typename?: 'Mutation', adminLogin?: { __typename?: 'LoginResponse', token: string, user: { __typename?: 'User', id: string, identifier: string, role: string } } | null };


export const TicketStatsDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"TicketStats"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"eventID"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getTicketStatsForEvent"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"event_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"eventID"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"total_amount"}},{"kind":"Field","name":{"kind":"Name","value":"total_tip_amount"}},{"kind":"Field","name":{"kind":"Name","value":"total_tickets_sold"}},{"kind":"Field","name":{"kind":"Name","value":"ticket_type_stats"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"num_of_tickets"}},{"kind":"Field","name":{"kind":"Name","value":"ticket_type"}}]}}]}}]}}]} as unknown as DocumentNode<TicketStatsQuery, TicketStatsQueryVariables>;
export const AddSpotlightDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"AddSpotlight"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"image"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Upload"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"url"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"isExternal"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Boolean"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"order"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"addSpotlight"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"image"},"value":{"kind":"Variable","name":{"kind":"Name","value":"image"}}},{"kind":"Argument","name":{"kind":"Name","value":"url"},"value":{"kind":"Variable","name":{"kind":"Name","value":"url"}}},{"kind":"Argument","name":{"kind":"Name","value":"is_external"},"value":{"kind":"Variable","name":{"kind":"Name","value":"isExternal"}}},{"kind":"Argument","name":{"kind":"Name","value":"order"},"value":{"kind":"Variable","name":{"kind":"Name","value":"order"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"message"}}]}}]}}]} as unknown as DocumentNode<AddSpotlightMutation, AddSpotlightMutationVariables>;
export const CreateSportEventPaymentDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"CreateSportEventPayment"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"ticketTypeId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"mobileNumber"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"numberOfTickets"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"username"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"seatNumbers"}},"type":{"kind":"NonNullType","type":{"kind":"ListType","type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"tipAmount"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Float"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"tipMEssage"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"createSportEventPayment"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"ticket_type_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"ticketTypeId"}}},{"kind":"Argument","name":{"kind":"Name","value":"mobile_number"},"value":{"kind":"Variable","name":{"kind":"Name","value":"mobileNumber"}}},{"kind":"Argument","name":{"kind":"Name","value":"number_of_tickets"},"value":{"kind":"Variable","name":{"kind":"Name","value":"numberOfTickets"}}},{"kind":"Argument","name":{"kind":"Name","value":"username"},"value":{"kind":"Variable","name":{"kind":"Name","value":"username"}}},{"kind":"Argument","name":{"kind":"Name","value":"seat_numbers"},"value":{"kind":"Variable","name":{"kind":"Name","value":"seatNumbers"}}},{"kind":"Argument","name":{"kind":"Name","value":"tip_amount"},"value":{"kind":"Variable","name":{"kind":"Name","value":"tipAmount"}}},{"kind":"Argument","name":{"kind":"Name","value":"tip_message"},"value":{"kind":"Variable","name":{"kind":"Name","value":"tipMEssage"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"order_id"}},{"kind":"Field","name":{"kind":"Name","value":"payment_id"}},{"kind":"Field","name":{"kind":"Name","value":"provider_type"}},{"kind":"Field","name":{"kind":"Name","value":"goto_url"}},{"kind":"Field","name":{"kind":"Name","value":"username"}},{"kind":"Field","name":{"kind":"Name","value":"mobile_number"}}]}}]}}]} as unknown as DocumentNode<CreateSportEventPaymentMutation, CreateSportEventPaymentMutationVariables>;
export const CreateEventDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"CreateEvent"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"name"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"description"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"location"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"eventTicketType"}},"type":{"kind":"ListType","type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"CreateEventTicketTypeInput"}}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"eventImages"}},"type":{"kind":"NonNullType","type":{"kind":"ListType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Upload"}}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"startDate"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"DateTime"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"endDate"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"DateTime"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"startBookingDate"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"DateTime"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"address"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"orgName"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"orgContactNumber"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"isPublished"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Boolean"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"isPrivateEvent"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Boolean"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"faq"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"artists"}},"type":{"kind":"ListType","type":{"kind":"NamedType","name":{"kind":"Name","value":"EventArtistsInput"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"openMicLink"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"city"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"eventType"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"EventType"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"createEvent"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"name"},"value":{"kind":"Variable","name":{"kind":"Name","value":"name"}}},{"kind":"Argument","name":{"kind":"Name","value":"description"},"value":{"kind":"Variable","name":{"kind":"Name","value":"description"}}},{"kind":"Argument","name":{"kind":"Name","value":"location"},"value":{"kind":"Variable","name":{"kind":"Name","value":"location"}}},{"kind":"Argument","name":{"kind":"Name","value":"event_ticket_types"},"value":{"kind":"Variable","name":{"kind":"Name","value":"eventTicketType"}}},{"kind":"Argument","name":{"kind":"Name","value":"event_images"},"value":{"kind":"Variable","name":{"kind":"Name","value":"eventImages"}}},{"kind":"Argument","name":{"kind":"Name","value":"start_date"},"value":{"kind":"Variable","name":{"kind":"Name","value":"startDate"}}},{"kind":"Argument","name":{"kind":"Name","value":"end_date"},"value":{"kind":"Variable","name":{"kind":"Name","value":"endDate"}}},{"kind":"Argument","name":{"kind":"Name","value":"start_booking_date"},"value":{"kind":"Variable","name":{"kind":"Name","value":"startBookingDate"}}},{"kind":"Argument","name":{"kind":"Name","value":"organizer_name"},"value":{"kind":"Variable","name":{"kind":"Name","value":"orgName"}}},{"kind":"Argument","name":{"kind":"Name","value":"org_contact_number"},"value":{"kind":"Variable","name":{"kind":"Name","value":"orgContactNumber"}}},{"kind":"Argument","name":{"kind":"Name","value":"address"},"value":{"kind":"Variable","name":{"kind":"Name","value":"address"}}},{"kind":"Argument","name":{"kind":"Name","value":"is_published"},"value":{"kind":"Variable","name":{"kind":"Name","value":"isPublished"}}},{"kind":"Argument","name":{"kind":"Name","value":"is_private_event"},"value":{"kind":"Variable","name":{"kind":"Name","value":"isPrivateEvent"}}},{"kind":"Argument","name":{"kind":"Name","value":"faq"},"value":{"kind":"Variable","name":{"kind":"Name","value":"faq"}}},{"kind":"Argument","name":{"kind":"Name","value":"artists"},"value":{"kind":"Variable","name":{"kind":"Name","value":"artists"}}},{"kind":"Argument","name":{"kind":"Name","value":"open_mic_link"},"value":{"kind":"Variable","name":{"kind":"Name","value":"openMicLink"}}},{"kind":"Argument","name":{"kind":"Name","value":"city"},"value":{"kind":"Variable","name":{"kind":"Name","value":"city"}}},{"kind":"Argument","name":{"kind":"Name","value":"event_type"},"value":{"kind":"Variable","name":{"kind":"Name","value":"eventType"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"is_published"}}]}}]}}]} as unknown as DocumentNode<CreateEventMutation, CreateEventMutationVariables>;
export const CreatePaymentDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"CreatePayment"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"eventTicketTypeId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"mobileNumber"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"username"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"numberOfTickets"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"couponHash"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"isGift"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Boolean"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"recipientName"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"recipientPhone"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"recipientMessage"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"tipAmount"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Float"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"tipMessage"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"createPayment"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"event_ticket_type_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"eventTicketTypeId"}}},{"kind":"Argument","name":{"kind":"Name","value":"mobile_number"},"value":{"kind":"Variable","name":{"kind":"Name","value":"mobileNumber"}}},{"kind":"Argument","name":{"kind":"Name","value":"username"},"value":{"kind":"Variable","name":{"kind":"Name","value":"username"}}},{"kind":"Argument","name":{"kind":"Name","value":"num_of_tickets"},"value":{"kind":"Variable","name":{"kind":"Name","value":"numberOfTickets"}}},{"kind":"Argument","name":{"kind":"Name","value":"coupon_hash"},"value":{"kind":"Variable","name":{"kind":"Name","value":"couponHash"}}},{"kind":"Argument","name":{"kind":"Name","value":"is_gift"},"value":{"kind":"Variable","name":{"kind":"Name","value":"isGift"}}},{"kind":"Argument","name":{"kind":"Name","value":"recipient_name"},"value":{"kind":"Variable","name":{"kind":"Name","value":"recipientName"}}},{"kind":"Argument","name":{"kind":"Name","value":"recipient_phone"},"value":{"kind":"Variable","name":{"kind":"Name","value":"recipientPhone"}}},{"kind":"Argument","name":{"kind":"Name","value":"recipient_message"},"value":{"kind":"Variable","name":{"kind":"Name","value":"recipientMessage"}}},{"kind":"Argument","name":{"kind":"Name","value":"tip_amount"},"value":{"kind":"Variable","name":{"kind":"Name","value":"tipAmount"}}},{"kind":"Argument","name":{"kind":"Name","value":"tip_message"},"value":{"kind":"Variable","name":{"kind":"Name","value":"tipMessage"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"order_id"}},{"kind":"Field","name":{"kind":"Name","value":"payment_id"}},{"kind":"Field","name":{"kind":"Name","value":"provider_type"}},{"kind":"Field","name":{"kind":"Name","value":"goto_url"}},{"kind":"Field","name":{"kind":"Name","value":"mobile_number"}},{"kind":"Field","name":{"kind":"Name","value":"username"}}]}}]}}]} as unknown as DocumentNode<CreatePaymentMutation, CreatePaymentMutationVariables>;
export const DeleteSpotlightDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"DeleteSpotlight"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"deleteSpotlight"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"message"}}]}}]}}]} as unknown as DocumentNode<DeleteSpotlightMutation, DeleteSpotlightMutationVariables>;
export const LogoutDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"Logout"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"logout"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"message"}}]}}]}}]} as unknown as DocumentNode<LogoutMutation, LogoutMutationVariables>;
export const RedeemCouponDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"RedeemCoupon"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"coupon"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"eventId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"redeemCoupon"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"coupon"},"value":{"kind":"Variable","name":{"kind":"Name","value":"coupon"}}},{"kind":"Argument","name":{"kind":"Name","value":"event_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"eventId"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"rc"}},{"kind":"Field","name":{"kind":"Name","value":"amount"}}]}}]}}]} as unknown as DocumentNode<RedeemCouponMutation, RedeemCouponMutationVariables>;
export const UpdateSpotlightDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"UpdateSpotlight"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"image"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Upload"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"url"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"order"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"isExternal"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Boolean"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"updateSpotlight"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}},{"kind":"Argument","name":{"kind":"Name","value":"image"},"value":{"kind":"Variable","name":{"kind":"Name","value":"image"}}},{"kind":"Argument","name":{"kind":"Name","value":"url"},"value":{"kind":"Variable","name":{"kind":"Name","value":"url"}}},{"kind":"Argument","name":{"kind":"Name","value":"is_external"},"value":{"kind":"Variable","name":{"kind":"Name","value":"isExternal"}}},{"kind":"Argument","name":{"kind":"Name","value":"order"},"value":{"kind":"Variable","name":{"kind":"Name","value":"order"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"message"}}]}}]}}]} as unknown as DocumentNode<UpdateSpotlightMutation, UpdateSpotlightMutationVariables>;
export const UpdateEventDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"UpdateEvent"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"name"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"description"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"location"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"eventImages"}},"type":{"kind":"ListType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Upload"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"address"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"orgName"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"orgContactNumber"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"isPublished"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Boolean"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"isPrivateEvent"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Boolean"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"startDate"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"DateTime"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"endDate"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"DateTime"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"startBookingDate"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"DateTime"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"openMicLink"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"seatingPlan"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Upload"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"city"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"faq"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"updateEvent"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}},{"kind":"Argument","name":{"kind":"Name","value":"name"},"value":{"kind":"Variable","name":{"kind":"Name","value":"name"}}},{"kind":"Argument","name":{"kind":"Name","value":"description"},"value":{"kind":"Variable","name":{"kind":"Name","value":"description"}}},{"kind":"Argument","name":{"kind":"Name","value":"location"},"value":{"kind":"Variable","name":{"kind":"Name","value":"location"}}},{"kind":"Argument","name":{"kind":"Name","value":"event_images"},"value":{"kind":"Variable","name":{"kind":"Name","value":"eventImages"}}},{"kind":"Argument","name":{"kind":"Name","value":"organizer_name"},"value":{"kind":"Variable","name":{"kind":"Name","value":"orgName"}}},{"kind":"Argument","name":{"kind":"Name","value":"org_contact_number"},"value":{"kind":"Variable","name":{"kind":"Name","value":"orgContactNumber"}}},{"kind":"Argument","name":{"kind":"Name","value":"address"},"value":{"kind":"Variable","name":{"kind":"Name","value":"address"}}},{"kind":"Argument","name":{"kind":"Name","value":"is_published"},"value":{"kind":"Variable","name":{"kind":"Name","value":"isPublished"}}},{"kind":"Argument","name":{"kind":"Name","value":"is_private_event"},"value":{"kind":"Variable","name":{"kind":"Name","value":"isPrivateEvent"}}},{"kind":"Argument","name":{"kind":"Name","value":"start_date"},"value":{"kind":"Variable","name":{"kind":"Name","value":"startDate"}}},{"kind":"Argument","name":{"kind":"Name","value":"end_date"},"value":{"kind":"Variable","name":{"kind":"Name","value":"endDate"}}},{"kind":"Argument","name":{"kind":"Name","value":"start_booking_date"},"value":{"kind":"Variable","name":{"kind":"Name","value":"startBookingDate"}}},{"kind":"Argument","name":{"kind":"Name","value":"open_mic_link"},"value":{"kind":"Variable","name":{"kind":"Name","value":"openMicLink"}}},{"kind":"Argument","name":{"kind":"Name","value":"seating_plan"},"value":{"kind":"Variable","name":{"kind":"Name","value":"seatingPlan"}}},{"kind":"Argument","name":{"kind":"Name","value":"city"},"value":{"kind":"Variable","name":{"kind":"Name","value":"city"}}},{"kind":"Argument","name":{"kind":"Name","value":"faq"},"value":{"kind":"Variable","name":{"kind":"Name","value":"faq"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}}]}}]}}]} as unknown as DocumentNode<UpdateEventMutation, UpdateEventMutationVariables>;
export const UpsertEventArtistDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"UpsertEventArtist"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"name"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"avatar"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Upload"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"order"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"eventId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"social_link"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"upsertEventArtist"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}},{"kind":"Argument","name":{"kind":"Name","value":"name"},"value":{"kind":"Variable","name":{"kind":"Name","value":"name"}}},{"kind":"Argument","name":{"kind":"Name","value":"avatar"},"value":{"kind":"Variable","name":{"kind":"Name","value":"avatar"}}},{"kind":"Argument","name":{"kind":"Name","value":"order"},"value":{"kind":"Variable","name":{"kind":"Name","value":"order"}}},{"kind":"Argument","name":{"kind":"Name","value":"event_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"eventId"}}},{"kind":"Argument","name":{"kind":"Name","value":"social_link"},"value":{"kind":"Variable","name":{"kind":"Name","value":"social_link"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}}]}}]}}]} as unknown as DocumentNode<UpsertEventArtistMutation, UpsertEventArtistMutationVariables>;
export const EventBySlugDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"EventBySlug"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"slug"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"eventBySlug"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"slug"},"value":{"kind":"Variable","name":{"kind":"Name","value":"slug"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"description"}},{"kind":"Field","name":{"kind":"Name","value":"type"}},{"kind":"Field","name":{"kind":"Name","value":"location"}},{"kind":"Field","name":{"kind":"Name","value":"eventTicketTypes"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"ticket_type"}},{"kind":"Field","name":{"kind":"Name","value":"price"}},{"kind":"Field","name":{"kind":"Name","value":"maximum_capacity"}},{"kind":"Field","name":{"kind":"Name","value":"status"}},{"kind":"Field","name":{"kind":"Name","value":"is_convenience_fee_incl"}},{"kind":"Field","name":{"kind":"Name","value":"is_gst_incl"}}]}},{"kind":"Field","name":{"kind":"Name","value":"seatingPlan"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}}]}},{"kind":"Field","name":{"kind":"Name","value":"images"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}},{"kind":"Field","name":{"kind":"Name","value":"width"}},{"kind":"Field","name":{"kind":"Name","value":"height"}}]}},{"kind":"Field","name":{"kind":"Name","value":"slug"}},{"kind":"Field","name":{"kind":"Name","value":"start_date"}},{"kind":"Field","name":{"kind":"Name","value":"end_date"}},{"kind":"Field","name":{"kind":"Name","value":"start_booking_date"}},{"kind":"Field","name":{"kind":"Name","value":"address"}},{"kind":"Field","name":{"kind":"Name","value":"org_contact_number"}},{"kind":"Field","name":{"kind":"Name","value":"organizer_name"}},{"kind":"Field","name":{"kind":"Name","value":"faq"}},{"kind":"Field","name":{"kind":"Name","value":"artists"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"avatar"}},{"kind":"Field","name":{"kind":"Name","value":"social_link"}}]}},{"kind":"Field","name":{"kind":"Name","value":"open_mic_link"}},{"kind":"Field","name":{"kind":"Name","value":"coupons"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}}]}}]}}]}}]} as unknown as DocumentNode<EventBySlugQuery, EventBySlugQueryVariables>;
export const EventsDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"Events"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"page"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"currentDatetime"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"DateTime"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"name"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"event_types"}},"type":{"kind":"ListType","type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"first"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getEvents"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"page"},"value":{"kind":"Variable","name":{"kind":"Name","value":"page"}}},{"kind":"Argument","name":{"kind":"Name","value":"current_datetime"},"value":{"kind":"Variable","name":{"kind":"Name","value":"currentDatetime"}}},{"kind":"Argument","name":{"kind":"Name","value":"name"},"value":{"kind":"Variable","name":{"kind":"Name","value":"name"}}},{"kind":"Argument","name":{"kind":"Name","value":"event_types"},"value":{"kind":"Variable","name":{"kind":"Name","value":"event_types"}}},{"kind":"Argument","name":{"kind":"Name","value":"first"},"value":{"kind":"Variable","name":{"kind":"Name","value":"first"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"data"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"start_date"}},{"kind":"Field","name":{"kind":"Name","value":"end_date"}},{"kind":"Field","name":{"kind":"Name","value":"type"}},{"kind":"Field","name":{"kind":"Name","value":"images"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}},{"kind":"Field","name":{"kind":"Name","value":"width"}},{"kind":"Field","name":{"kind":"Name","value":"height"}}]}},{"kind":"Field","name":{"kind":"Name","value":"slug"}},{"kind":"Field","name":{"kind":"Name","value":"eventTicketTypes"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"price"}},{"kind":"Field","name":{"kind":"Name","value":"ticket_type"}}]}},{"kind":"Field","name":{"kind":"Name","value":"start_booking_date"}},{"kind":"Field","name":{"kind":"Name","value":"open_mic_link"}},{"kind":"Field","name":{"kind":"Name","value":"city"}}]}},{"kind":"Field","name":{"kind":"Name","value":"paginatorInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"lastPage"}},{"kind":"Field","name":{"kind":"Name","value":"hasMorePages"}},{"kind":"Field","name":{"kind":"Name","value":"currentPage"}}]}}]}}]}}]} as unknown as DocumentNode<EventsQuery, EventsQueryVariables>;
export const EventsForSuperAdminDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"EventsForSuperAdmin"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"page"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"currentDatetime"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"DateTime"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"name"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"first"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"type"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"events"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"page"},"value":{"kind":"Variable","name":{"kind":"Name","value":"page"}}},{"kind":"Argument","name":{"kind":"Name","value":"current_datetime"},"value":{"kind":"Variable","name":{"kind":"Name","value":"currentDatetime"}}},{"kind":"Argument","name":{"kind":"Name","value":"name"},"value":{"kind":"Variable","name":{"kind":"Name","value":"name"}}},{"kind":"Argument","name":{"kind":"Name","value":"first"},"value":{"kind":"Variable","name":{"kind":"Name","value":"first"}}},{"kind":"Argument","name":{"kind":"Name","value":"type"},"value":{"kind":"Variable","name":{"kind":"Name","value":"type"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"data"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}}]}}]}}]}}]} as unknown as DocumentNode<EventsForSuperAdminQuery, EventsForSuperAdminQueryVariables>;
export const GetEventsDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"getEvents"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"first"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"page"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"name"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"eventTypes"}},"type":{"kind":"ListType","type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"sortOrder"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getEvents"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"first"},"value":{"kind":"Variable","name":{"kind":"Name","value":"first"}}},{"kind":"Argument","name":{"kind":"Name","value":"page"},"value":{"kind":"Variable","name":{"kind":"Name","value":"page"}}},{"kind":"Argument","name":{"kind":"Name","value":"name"},"value":{"kind":"Variable","name":{"kind":"Name","value":"name"}}},{"kind":"Argument","name":{"kind":"Name","value":"event_types"},"value":{"kind":"Variable","name":{"kind":"Name","value":"eventTypes"}}},{"kind":"Argument","name":{"kind":"Name","value":"sort_order"},"value":{"kind":"Variable","name":{"kind":"Name","value":"sortOrder"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"data"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"type"}},{"kind":"Field","name":{"kind":"Name","value":"start_date"}},{"kind":"Field","name":{"kind":"Name","value":"end_date"}},{"kind":"Field","name":{"kind":"Name","value":"images"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}},{"kind":"Field","name":{"kind":"Name","value":"width"}},{"kind":"Field","name":{"kind":"Name","value":"height"}}]}},{"kind":"Field","name":{"kind":"Name","value":"slug"}},{"kind":"Field","name":{"kind":"Name","value":"eventTicketTypes"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"price"}},{"kind":"Field","name":{"kind":"Name","value":"ticket_type"}}]}},{"kind":"Field","name":{"kind":"Name","value":"sportTicketTypes"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"price"}},{"kind":"Field","name":{"kind":"Name","value":"ticket_type"}}]}},{"kind":"Field","name":{"kind":"Name","value":"start_booking_date"}},{"kind":"Field","name":{"kind":"Name","value":"open_mic_link"}},{"kind":"Field","name":{"kind":"Name","value":"city"}}]}},{"kind":"Field","name":{"kind":"Name","value":"paginatorInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"hasMorePages"}},{"kind":"Field","name":{"kind":"Name","value":"currentPage"}},{"kind":"Field","name":{"kind":"Name","value":"lastPage"}}]}}]}}]}}]} as unknown as DocumentNode<GetEventsQuery, GetEventsQueryVariables>;
export const GetSeatsForSportsEventDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetSeatsForSportsEvent"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"ticketTypeId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getSeatsForSportsEvent"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"ticket_type_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"ticketTypeId"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"all_seats"}},{"kind":"Field","name":{"kind":"Name","value":"available_seats"}},{"kind":"Field","name":{"kind":"Name","value":"price"}},{"kind":"Field","name":{"kind":"Name","value":"convenience_fee"}}]}}]}}]} as unknown as DocumentNode<GetSeatsForSportsEventQuery, GetSeatsForSportsEventQueryVariables>;
export const GetSpotlightListDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetSpotlightList"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getSpotlightList"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"app_image_id"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"is_external"}},{"kind":"Field","name":{"kind":"Name","value":"order"}},{"kind":"Field","name":{"kind":"Name","value":"image"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}}]}}]}}]} as unknown as DocumentNode<GetSpotlightListQuery, GetSpotlightListQueryVariables>;
export const GetConvenienceFeeDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetConvenienceFee"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"ticketTypeId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getConvenienceFee"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"ticket_type_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"ticketTypeId"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"amount"}}]}}]}}]} as unknown as DocumentNode<GetConvenienceFeeQuery, GetConvenienceFeeQueryVariables>;
export const GetCouponsForEventDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetCouponsForEvent"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"eventId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getCouponsForEvent"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"event_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"eventId"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"expiry_date"}},{"kind":"Field","name":{"kind":"Name","value":"max_usage"}},{"kind":"Field","name":{"kind":"Name","value":"is_published"}},{"kind":"Field","name":{"kind":"Name","value":"discount_percent"}},{"kind":"Field","name":{"kind":"Name","value":"coupon_usage_count"}}]}}]}}]} as unknown as DocumentNode<GetCouponsForEventQuery, GetCouponsForEventQueryVariables>;
export const EventByIdDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"EventById"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"eventById"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"description"}},{"kind":"Field","name":{"kind":"Name","value":"location"}},{"kind":"Field","name":{"kind":"Name","value":"images"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}},{"kind":"Field","name":{"kind":"Name","value":"width"}},{"kind":"Field","name":{"kind":"Name","value":"height"}}]}},{"kind":"Field","name":{"kind":"Name","value":"seatingPlan"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}},{"kind":"Field","name":{"kind":"Name","value":"address"}},{"kind":"Field","name":{"kind":"Name","value":"org_contact_number"}},{"kind":"Field","name":{"kind":"Name","value":"is_published"}},{"kind":"Field","name":{"kind":"Name","value":"is_private_event"}},{"kind":"Field","name":{"kind":"Name","value":"organizer_name"}},{"kind":"Field","name":{"kind":"Name","value":"eventTicketTypes"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"ticket_type"}},{"kind":"Field","name":{"kind":"Name","value":"price"}},{"kind":"Field","name":{"kind":"Name","value":"maximum_capacity"}}]}},{"kind":"Field","name":{"kind":"Name","value":"slug"}},{"kind":"Field","name":{"kind":"Name","value":"start_date"}},{"kind":"Field","name":{"kind":"Name","value":"end_date"}},{"kind":"Field","name":{"kind":"Name","value":"start_booking_date"}},{"kind":"Field","name":{"kind":"Name","value":"artists"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"avatar"}},{"kind":"Field","name":{"kind":"Name","value":"social_link"}},{"kind":"Field","name":{"kind":"Name","value":"order"}}]}},{"kind":"Field","name":{"kind":"Name","value":"open_mic_link"}},{"kind":"Field","name":{"kind":"Name","value":"city"}},{"kind":"Field","name":{"kind":"Name","value":"faq"}}]}}]}}]} as unknown as DocumentNode<EventByIdQuery, EventByIdQueryVariables>;
export const GetGuestListDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetGuestList"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"eventId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"first"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"page"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"userName"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"mobileNumber"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"ticketTypeId"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getGuestList"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"event_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"eventId"}}},{"kind":"Argument","name":{"kind":"Name","value":"first"},"value":{"kind":"Variable","name":{"kind":"Name","value":"first"}}},{"kind":"Argument","name":{"kind":"Name","value":"page"},"value":{"kind":"Variable","name":{"kind":"Name","value":"page"}}},{"kind":"Argument","name":{"kind":"Name","value":"username"},"value":{"kind":"Variable","name":{"kind":"Name","value":"userName"}}},{"kind":"Argument","name":{"kind":"Name","value":"mobile_number"},"value":{"kind":"Variable","name":{"kind":"Name","value":"mobileNumber"}}},{"kind":"Argument","name":{"kind":"Name","value":"ticket_type_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"ticketTypeId"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}},{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"SportGuestList"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"data"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"username"}},{"kind":"Field","name":{"kind":"Name","value":"mobile_number"}},{"kind":"Field","name":{"kind":"Name","value":"number_of_tickets"}},{"kind":"Field","name":{"kind":"Name","value":"seat_numbers"}},{"kind":"Field","name":{"kind":"Name","value":"scanned_at"}},{"kind":"Field","name":{"kind":"Name","value":"paymentOrder"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"paymentable"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}},{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"PhonePePaymentOrder"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"order_id"}},{"kind":"Field","name":{"kind":"Name","value":"payment_id"}}]}},{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"RzpayPaymentOrder"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"order_id"}},{"kind":"Field","name":{"kind":"Name","value":"payment_id"}}]}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"eventTicketType"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"ticket_type"}},{"kind":"Field","name":{"kind":"Name","value":"slug"}}]}},{"kind":"Field","name":{"kind":"Name","value":"tip_amount"}},{"kind":"Field","name":{"kind":"Name","value":"tip_message"}}]}},{"kind":"Field","name":{"kind":"Name","value":"paginatorInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"lastPage"}},{"kind":"Field","name":{"kind":"Name","value":"total"}},{"kind":"Field","name":{"kind":"Name","value":"currentPage"}},{"kind":"Field","name":{"kind":"Name","value":"hasMorePages"}}]}}]}},{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"GeneralGuestList"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"data"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"username"}},{"kind":"Field","name":{"kind":"Name","value":"mobile_number"}},{"kind":"Field","name":{"kind":"Name","value":"number_of_tickets"}},{"kind":"Field","name":{"kind":"Name","value":"scanned_at"}},{"kind":"Field","name":{"kind":"Name","value":"paymentOrder"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"paymentable"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}},{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"PhonePePaymentOrder"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"order_id"}},{"kind":"Field","name":{"kind":"Name","value":"payment_id"}}]}},{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"RzpayPaymentOrder"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"order_id"}},{"kind":"Field","name":{"kind":"Name","value":"payment_id"}}]}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"eventTicketType"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"ticket_type"}}]}},{"kind":"Field","name":{"kind":"Name","value":"tip_amount"}},{"kind":"Field","name":{"kind":"Name","value":"tip_message"}}]}},{"kind":"Field","name":{"kind":"Name","value":"paginatorInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"lastPage"}},{"kind":"Field","name":{"kind":"Name","value":"total"}},{"kind":"Field","name":{"kind":"Name","value":"currentPage"}},{"kind":"Field","name":{"kind":"Name","value":"hasMorePages"}}]}}]}}]}}]}}]} as unknown as DocumentNode<GetGuestListQuery, GetGuestListQueryVariables>;
export const GetInviteesDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetInvitees"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"username"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"mobileNumber"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"eventId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"first"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"page"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getInvitees"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"username"},"value":{"kind":"Variable","name":{"kind":"Name","value":"username"}}},{"kind":"Argument","name":{"kind":"Name","value":"mobile_number"},"value":{"kind":"Variable","name":{"kind":"Name","value":"mobileNumber"}}},{"kind":"Argument","name":{"kind":"Name","value":"event_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"eventId"}}},{"kind":"Argument","name":{"kind":"Name","value":"first"},"value":{"kind":"Variable","name":{"kind":"Name","value":"first"}}},{"kind":"Argument","name":{"kind":"Name","value":"page"},"value":{"kind":"Variable","name":{"kind":"Name","value":"page"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"data"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"username"}},{"kind":"Field","name":{"kind":"Name","value":"mobile_number"}},{"kind":"Field","name":{"kind":"Name","value":"whatsapp_sent_status"}},{"kind":"Field","name":{"kind":"Name","value":"number_of_tickets"}},{"kind":"Field","name":{"kind":"Name","value":"scanned_at"}},{"kind":"Field","name":{"kind":"Name","value":"paymentOrder"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"paymentable"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}},{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"PhonePePaymentOrder"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"order_id"}},{"kind":"Field","name":{"kind":"Name","value":"payment_id"}}]}},{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"RzpayPaymentOrder"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"order_id"}},{"kind":"Field","name":{"kind":"Name","value":"payment_id"}}]}}]}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"paginatorInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"lastPage"}},{"kind":"Field","name":{"kind":"Name","value":"total"}},{"kind":"Field","name":{"kind":"Name","value":"hasMorePages"}},{"kind":"Field","name":{"kind":"Name","value":"currentPage"}}]}}]}}]}}]} as unknown as DocumentNode<GetInviteesQuery, GetInviteesQueryVariables>;
export const GetMeDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetMe"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getMe"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"user"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"role"}},{"kind":"Field","name":{"kind":"Name","value":"identifier"}}]}},{"kind":"Field","name":{"kind":"Name","value":"token"}}]}}]}}]} as unknown as DocumentNode<GetMeQuery, GetMeQueryVariables>;
export const MyEventsDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"MyEvents"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"page"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"first"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"myEvents"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"page"},"value":{"kind":"Variable","name":{"kind":"Name","value":"page"}}},{"kind":"Argument","name":{"kind":"Name","value":"first"},"value":{"kind":"Variable","name":{"kind":"Name","value":"first"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"data"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"description"}},{"kind":"Field","name":{"kind":"Name","value":"location"}},{"kind":"Field","name":{"kind":"Name","value":"eventTicketTypes"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"ticket_type"}},{"kind":"Field","name":{"kind":"Name","value":"price"}},{"kind":"Field","name":{"kind":"Name","value":"maximum_capacity"}}]}},{"kind":"Field","name":{"kind":"Name","value":"images"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}},{"kind":"Field","name":{"kind":"Name","value":"width"}},{"kind":"Field","name":{"kind":"Name","value":"height"}}]}},{"kind":"Field","name":{"kind":"Name","value":"slug"}},{"kind":"Field","name":{"kind":"Name","value":"start_date"}},{"kind":"Field","name":{"kind":"Name","value":"end_date"}},{"kind":"Field","name":{"kind":"Name","value":"address"}},{"kind":"Field","name":{"kind":"Name","value":"org_contact_number"}},{"kind":"Field","name":{"kind":"Name","value":"is_published"}},{"kind":"Field","name":{"kind":"Name","value":"is_private_event"}}]}},{"kind":"Field","name":{"kind":"Name","value":"paginatorInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"lastPage"}},{"kind":"Field","name":{"kind":"Name","value":"hasMorePages"}},{"kind":"Field","name":{"kind":"Name","value":"currentPage"}}]}}]}}]}}]} as unknown as DocumentNode<MyEventsQuery, MyEventsQueryVariables>;
export const GetOrganizersDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetOrganizers"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"page"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"name"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"organizerList"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"page"},"value":{"kind":"Variable","name":{"kind":"Name","value":"page"}}},{"kind":"Argument","name":{"kind":"Name","value":"name"},"value":{"kind":"Variable","name":{"kind":"Name","value":"name"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"hasMorePages"}},{"kind":"Field","name":{"kind":"Name","value":"currentPage"}},{"kind":"Field","name":{"kind":"Name","value":"total"}},{"kind":"Field","name":{"kind":"Name","value":"organizers"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"identifier"}}]}}]}}]}}]} as unknown as DocumentNode<GetOrganizersQuery, GetOrganizersQueryVariables>;
export const PastEventsDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"PastEvents"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"first"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"currentDatetime"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"DateTime"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"page"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"type"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getPastEvents"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"first"},"value":{"kind":"Variable","name":{"kind":"Name","value":"first"}}},{"kind":"Argument","name":{"kind":"Name","value":"current_datetime"},"value":{"kind":"Variable","name":{"kind":"Name","value":"currentDatetime"}}},{"kind":"Argument","name":{"kind":"Name","value":"page"},"value":{"kind":"Variable","name":{"kind":"Name","value":"page"}}},{"kind":"Argument","name":{"kind":"Name","value":"type"},"value":{"kind":"Variable","name":{"kind":"Name","value":"type"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"data"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"type"}},{"kind":"Field","name":{"kind":"Name","value":"end_date"}},{"kind":"Field","name":{"kind":"Name","value":"images"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}},{"kind":"Field","name":{"kind":"Name","value":"width"}},{"kind":"Field","name":{"kind":"Name","value":"height"}}]}},{"kind":"Field","name":{"kind":"Name","value":"slug"}}]}},{"kind":"Field","name":{"kind":"Name","value":"paginatorInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"hasMorePages"}},{"kind":"Field","name":{"kind":"Name","value":"currentPage"}},{"kind":"Field","name":{"kind":"Name","value":"lastPage"}}]}}]}}]}}]} as unknown as DocumentNode<PastEventsQuery, PastEventsQueryVariables>;
export const SportsBySlugDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"SportsBySlug"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"slug"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"eventBySlug"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"slug"},"value":{"kind":"Variable","name":{"kind":"Name","value":"slug"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"description"}},{"kind":"Field","name":{"kind":"Name","value":"location"}},{"kind":"Field","name":{"kind":"Name","value":"type"}},{"kind":"Field","name":{"kind":"Name","value":"sportTicketTypes"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"price"}},{"kind":"Field","name":{"kind":"Name","value":"ticket_type"}},{"kind":"Field","name":{"kind":"Name","value":"start_seat_number"}},{"kind":"Field","name":{"kind":"Name","value":"end_seat_number"}},{"kind":"Field","name":{"kind":"Name","value":"slug"}}]}},{"kind":"Field","name":{"kind":"Name","value":"seatingPlan"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}}]}},{"kind":"Field","name":{"kind":"Name","value":"images"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}},{"kind":"Field","name":{"kind":"Name","value":"width"}},{"kind":"Field","name":{"kind":"Name","value":"height"}}]}},{"kind":"Field","name":{"kind":"Name","value":"slug"}},{"kind":"Field","name":{"kind":"Name","value":"start_date"}},{"kind":"Field","name":{"kind":"Name","value":"end_date"}},{"kind":"Field","name":{"kind":"Name","value":"start_booking_date"}},{"kind":"Field","name":{"kind":"Name","value":"address"}},{"kind":"Field","name":{"kind":"Name","value":"org_contact_number"}},{"kind":"Field","name":{"kind":"Name","value":"organizer_name"}},{"kind":"Field","name":{"kind":"Name","value":"faq"}},{"kind":"Field","name":{"kind":"Name","value":"artists"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"avatar"}},{"kind":"Field","name":{"kind":"Name","value":"social_link"}}]}},{"kind":"Field","name":{"kind":"Name","value":"open_mic_link"}},{"kind":"Field","name":{"kind":"Name","value":"coupons"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}}]}}]}}]}}]} as unknown as DocumentNode<SportsBySlugQuery, SportsBySlugQueryVariables>;
export const SportsDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"Sports"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"page"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"currentDatetime"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"DateTime"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"name"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"type"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"events"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"page"},"value":{"kind":"Variable","name":{"kind":"Name","value":"page"}}},{"kind":"Argument","name":{"kind":"Name","value":"current_datetime"},"value":{"kind":"Variable","name":{"kind":"Name","value":"currentDatetime"}}},{"kind":"Argument","name":{"kind":"Name","value":"name"},"value":{"kind":"Variable","name":{"kind":"Name","value":"name"}}},{"kind":"Argument","name":{"kind":"Name","value":"type"},"value":{"kind":"Variable","name":{"kind":"Name","value":"type"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"data"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"start_date"}},{"kind":"Field","name":{"kind":"Name","value":"images"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}},{"kind":"Field","name":{"kind":"Name","value":"width"}},{"kind":"Field","name":{"kind":"Name","value":"height"}}]}},{"kind":"Field","name":{"kind":"Name","value":"slug"}},{"kind":"Field","name":{"kind":"Name","value":"sportTicketTypes"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"price"}},{"kind":"Field","name":{"kind":"Name","value":"ticket_type"}},{"kind":"Field","name":{"kind":"Name","value":"start_seat_number"}},{"kind":"Field","name":{"kind":"Name","value":"end_seat_number"}}]}},{"kind":"Field","name":{"kind":"Name","value":"start_booking_date"}},{"kind":"Field","name":{"kind":"Name","value":"open_mic_link"}},{"kind":"Field","name":{"kind":"Name","value":"city"}}]}},{"kind":"Field","name":{"kind":"Name","value":"paginatorInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"lastPage"}}]}}]}}]}}]} as unknown as DocumentNode<SportsQuery, SportsQueryVariables>;
export const AddStaffDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"AddStaff"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"username"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"password"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"addStaff"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"username"},"value":{"kind":"Variable","name":{"kind":"Name","value":"username"}}},{"kind":"Argument","name":{"kind":"Name","value":"password"},"value":{"kind":"Variable","name":{"kind":"Name","value":"password"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}}]}}]}}]} as unknown as DocumentNode<AddStaffMutation, AddStaffMutationVariables>;
export const GetStaffsDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetStaffs"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getStaffs"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"identifier"}},{"kind":"Field","name":{"kind":"Name","value":"role"}}]}}]}}]} as unknown as DocumentNode<GetStaffsQuery, GetStaffsQueryVariables>;
export const DeleteStaffDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"DeleteStaff"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"deleteStaff"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"message"}}]}}]}}]} as unknown as DocumentNode<DeleteStaffMutation, DeleteStaffMutationVariables>;
export const CreateCouponDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"CreateCoupon"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"data"}},"type":{"kind":"NonNullType","type":{"kind":"ListType","type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"CreateCouponInput"}}}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"createCoupon"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"data"},"value":{"kind":"Variable","name":{"kind":"Name","value":"data"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"message"}}]}}]}}]} as unknown as DocumentNode<CreateCouponMutation, CreateCouponMutationVariables>;
export const DeleteCouponDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"DeleteCoupon"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"couponId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"deleteCoupon"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"coupon_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"couponId"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}}]}}]}}]} as unknown as DocumentNode<DeleteCouponMutation, DeleteCouponMutationVariables>;
export const UpdateCouponDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"UpdateCoupon"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"couponId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"name"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"expiry_date"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"DateTime"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"event_id"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"discount_percent"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Float"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"max_usage"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"is_published"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Boolean"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"updateCoupon"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"coupon_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"couponId"}}},{"kind":"Argument","name":{"kind":"Name","value":"name"},"value":{"kind":"Variable","name":{"kind":"Name","value":"name"}}},{"kind":"Argument","name":{"kind":"Name","value":"expiry_date"},"value":{"kind":"Variable","name":{"kind":"Name","value":"expiry_date"}}},{"kind":"Argument","name":{"kind":"Name","value":"event_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"event_id"}}},{"kind":"Argument","name":{"kind":"Name","value":"discount_percent"},"value":{"kind":"Variable","name":{"kind":"Name","value":"discount_percent"}}},{"kind":"Argument","name":{"kind":"Name","value":"max_usage"},"value":{"kind":"Variable","name":{"kind":"Name","value":"max_usage"}}},{"kind":"Argument","name":{"kind":"Name","value":"is_published"},"value":{"kind":"Variable","name":{"kind":"Name","value":"is_published"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}}]}}]}}]} as unknown as DocumentNode<UpdateCouponMutation, UpdateCouponMutationVariables>;
export const AddInviteeDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"AddInvitee"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"eventId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"username"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"mobileNumber"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"numberOfTickets"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"addInvitee"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"event_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"eventId"}}},{"kind":"Argument","name":{"kind":"Name","value":"username"},"value":{"kind":"Variable","name":{"kind":"Name","value":"username"}}},{"kind":"Argument","name":{"kind":"Name","value":"mobile_number"},"value":{"kind":"Variable","name":{"kind":"Name","value":"mobileNumber"}}},{"kind":"Argument","name":{"kind":"Name","value":"number_of_tickets"},"value":{"kind":"Variable","name":{"kind":"Name","value":"numberOfTickets"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}}]}}]}}]} as unknown as DocumentNode<AddInviteeMutation, AddInviteeMutationVariables>;
export const DeleteEventDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"DeleteEvent"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"deleteEvent"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}}]}}]}}]} as unknown as DocumentNode<DeleteEventMutation, DeleteEventMutationVariables>;
export const DeleteImageByIdDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"DeleteImageById"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"deleteImageById"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"message"}}]}}]}}]} as unknown as DocumentNode<DeleteImageByIdMutation, DeleteImageByIdMutationVariables>;
export const DeleteEventArtistDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"DeleteEventArtist"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"deleteEventArtist"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"event_artist_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"message"}}]}}]}}]} as unknown as DocumentNode<DeleteEventArtistMutation, DeleteEventArtistMutationVariables>;
export const TicketAvailableDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"TicketAvailable"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"orderId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"paymentId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"isTicketDownloadable"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"order_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"orderId"}}},{"kind":"Argument","name":{"kind":"Name","value":"payment_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"paymentId"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"ticket_path"}},{"kind":"Field","name":{"kind":"Name","value":"amount"}}]}}]}}]} as unknown as DocumentNode<TicketAvailableQuery, TicketAvailableQueryVariables>;
export const GetRemainingSeatsForSeatedEventDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetRemainingSeatsForSeatedEvent"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"eventId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getRemainingSeatsForSeatedEvent"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"event_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"eventId"}}}]}]}}]} as unknown as DocumentNode<GetRemainingSeatsForSeatedEventQuery, GetRemainingSeatsForSeatedEventQueryVariables>;
export const OnBoardDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"OnBoard"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"email"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"name"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"address"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"mobileNumber"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"onboardOrganizer"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"email"},"value":{"kind":"Variable","name":{"kind":"Name","value":"email"}}},{"kind":"Argument","name":{"kind":"Name","value":"name"},"value":{"kind":"Variable","name":{"kind":"Name","value":"name"}}},{"kind":"Argument","name":{"kind":"Name","value":"address"},"value":{"kind":"Variable","name":{"kind":"Name","value":"address"}}},{"kind":"Argument","name":{"kind":"Name","value":"mobile_number"},"value":{"kind":"Variable","name":{"kind":"Name","value":"mobileNumber"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}}]}}]}}]} as unknown as DocumentNode<OnBoardMutation, OnBoardMutationVariables>;
export const UserRequestRefundDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"UserRequestRefund"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"eventId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"phoneNumber"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"userRequestRefund"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"event_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"eventId"}}},{"kind":"Argument","name":{"kind":"Name","value":"phone_number"},"value":{"kind":"Variable","name":{"kind":"Name","value":"phoneNumber"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"message"}}]}}]}}]} as unknown as DocumentNode<UserRequestRefundMutation, UserRequestRefundMutationVariables>;
export const SubmitQrCodeDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"SubmitQRCode"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"qrCode"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"submitQRCode"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"qr_code"},"value":{"kind":"Variable","name":{"kind":"Name","value":"qrCode"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"error"}}]}}]}}]} as unknown as DocumentNode<SubmitQrCodeMutation, SubmitQrCodeMutationVariables>;
export const StaffEventsDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"StaffEvents"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"adminEvents"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"eventTicketTypes"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"ticket_type"}},{"kind":"Field","name":{"kind":"Name","value":"price"}},{"kind":"Field","name":{"kind":"Name","value":"maximum_capacity"}}]}}]}}]}}]} as unknown as DocumentNode<StaffEventsQuery, StaffEventsQueryVariables>;
export const UpsertSettingDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"UpsertSetting"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"key"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"value"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"upsertSetting"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"key"},"value":{"kind":"Variable","name":{"kind":"Name","value":"key"}}},{"kind":"Argument","name":{"kind":"Name","value":"value"},"value":{"kind":"Variable","name":{"kind":"Name","value":"value"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}}]}}]}}]} as unknown as DocumentNode<UpsertSettingMutation, UpsertSettingMutationVariables>;
export const GetSettingByKeyDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetSettingByKey"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"key"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getSettingByKey"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"key"},"value":{"kind":"Variable","name":{"kind":"Name","value":"key"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"key"}},{"kind":"Field","name":{"kind":"Name","value":"value"}}]}}]}}]} as unknown as DocumentNode<GetSettingByKeyQuery, GetSettingByKeyQueryVariables>;
export const EventAttendeesDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"EventAttendees"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"eventID"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"first"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"page"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"username"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"mobileNumber"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"paymentStatus"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"eventAttendees"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"event_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"eventID"}}},{"kind":"Argument","name":{"kind":"Name","value":"first"},"value":{"kind":"Variable","name":{"kind":"Name","value":"first"}}},{"kind":"Argument","name":{"kind":"Name","value":"page"},"value":{"kind":"Variable","name":{"kind":"Name","value":"page"}}},{"kind":"Argument","name":{"kind":"Name","value":"username"},"value":{"kind":"Variable","name":{"kind":"Name","value":"username"}}},{"kind":"Argument","name":{"kind":"Name","value":"mobile_number"},"value":{"kind":"Variable","name":{"kind":"Name","value":"mobileNumber"}}},{"kind":"Argument","name":{"kind":"Name","value":"payment_status"},"value":{"kind":"Variable","name":{"kind":"Name","value":"paymentStatus"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"data"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"username"}},{"kind":"Field","name":{"kind":"Name","value":"mobile_number"}},{"kind":"Field","name":{"kind":"Name","value":"amount_paid"}},{"kind":"Field","name":{"kind":"Name","value":"paymentOrder"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"paymentable"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}},{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"PhonePePaymentOrder"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"order_id"}},{"kind":"Field","name":{"kind":"Name","value":"status"}},{"kind":"Field","name":{"kind":"Name","value":"refund_status"}},{"kind":"Field","name":{"kind":"Name","value":"refund_error_detail"}}]}},{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"RzpayPaymentOrder"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"order_id"}},{"kind":"Field","name":{"kind":"Name","value":"status"}},{"kind":"Field","name":{"kind":"Name","value":"refund_status"}},{"kind":"Field","name":{"kind":"Name","value":"refund_error_detail"}}]}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"payment_status"}},{"kind":"Field","name":{"kind":"Name","value":"number_of_tickets"}},{"kind":"Field","name":{"kind":"Name","value":"ticket_path"}}]}},{"kind":"Field","name":{"kind":"Name","value":"paginatorInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"lastPage"}},{"kind":"Field","name":{"kind":"Name","value":"total"}},{"kind":"Field","name":{"kind":"Name","value":"currentPage"}}]}}]}}]}}]} as unknown as DocumentNode<EventAttendeesQuery, EventAttendeesQueryVariables>;
export const IssueRefundDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"IssueRefund"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"amount"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Float"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"orderID"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"issueRefund"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"amount"},"value":{"kind":"Variable","name":{"kind":"Name","value":"amount"}}},{"kind":"Argument","name":{"kind":"Name","value":"order_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"orderID"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"status"}},{"kind":"Field","name":{"kind":"Name","value":"reason"}}]}}]}}]} as unknown as DocumentNode<IssueRefundMutation, IssueRefundMutationVariables>;
export const AddOrganizerDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"AddOrganizer"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"name"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"password"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"addOrganizer"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"name"},"value":{"kind":"Variable","name":{"kind":"Name","value":"name"}}},{"kind":"Argument","name":{"kind":"Name","value":"password"},"value":{"kind":"Variable","name":{"kind":"Name","value":"password"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"message"}}]}}]}}]} as unknown as DocumentNode<AddOrganizerMutation, AddOrganizerMutationVariables>;
export const UpdateOrganizerDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"UpdateOrganizer"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"name"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"password"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"updateOrganizer"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}},{"kind":"Argument","name":{"kind":"Name","value":"name"},"value":{"kind":"Variable","name":{"kind":"Name","value":"name"}}},{"kind":"Argument","name":{"kind":"Name","value":"password"},"value":{"kind":"Variable","name":{"kind":"Name","value":"password"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}}]}}]}}]} as unknown as DocumentNode<UpdateOrganizerMutation, UpdateOrganizerMutationVariables>;
export const CheckPhonepeHealthDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"CheckPhonepeHealth"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"checkPhonePeHealth"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"data"}}]}}]}}]} as unknown as DocumentNode<CheckPhonepeHealthQuery, CheckPhonepeHealthQueryVariables>;
export const NotifyUpcomingEventDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"NotifyUpcomingEvent"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"notifyUpcomingEvent"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}}]}}]}}]} as unknown as DocumentNode<NotifyUpcomingEventMutation, NotifyUpcomingEventMutationVariables>;
export const LoginDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"Login"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"identifier"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"password"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"adminLogin"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"username"},"value":{"kind":"Variable","name":{"kind":"Name","value":"identifier"}}},{"kind":"Argument","name":{"kind":"Name","value":"password"},"value":{"kind":"Variable","name":{"kind":"Name","value":"password"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"user"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"identifier"}},{"kind":"Field","name":{"kind":"Name","value":"role"}}]}},{"kind":"Field","name":{"kind":"Name","value":"token"}}]}}]}}]} as unknown as DocumentNode<LoginMutation, LoginMutationVariables>;