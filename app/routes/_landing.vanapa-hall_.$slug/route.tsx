import getGraphqlClient from "@/lib/hooks/get-graphql-client"
import PaymentMenuVanapaHall from "./payment-menu-vanapa-hall"
import { ClientError } from "graphql-request"
import { redirect, useLoaderData } from "react-router"
import { GET_EVENT_BY_SLUG } from "@/lib/graphql/queries"
import { GET_REMAINING_SEATS } from "./graphql"
import type { Route } from "./+types/route"
import { useQuery } from "@tanstack/react-query"

export async function loader({ params }: Route.LoaderArgs) {
  const graphqlClient = getGraphqlClient()
  try {
    const eventSlug = await graphqlClient.request(GET_EVENT_BY_SLUG, {
      slug: params.slug!,
    })
    // const remainingSeats = await graphqlClient.request(GET_REMAINING_SEATS, {
    //   eventId: "57",
    // })

    return {
      eventData: eventSlug.eventBySlug,
      // remainingSeats: remainingSeats.getRemainingSeatsForSeatedEvent,
    }
  } catch (error) {
    if (error instanceof ClientError && error?.response?.status === 429) {
      return redirect(`/too-many-requests`)
    }
    throw error
  }
}

const VanapaHall = () => {
  const { eventData } = useLoaderData<typeof loader>()

  const { data, isLoading, isError } = useQuery({
    queryKey: ["remaining-seats", eventData!.id],
    queryFn: async () => {
      const graphql = getGraphqlClient()
      return await graphql.request(GET_REMAINING_SEATS, {
        eventId: eventData!.id,
      })
    },
    enabled: !!eventData,
  })

  console.log("data", data)

  // console.log("remainingSeats", remainingSeats)
  console.log("eventData", eventData)

  return <PaymentMenuVanapaHall />
}

export default VanapaHall
